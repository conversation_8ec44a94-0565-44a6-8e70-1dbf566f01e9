#!/usr/bin/env python3
"""
Test Signal Generation
=====================

This script tests the EMA crossover signal generation using mock data
to verify that signals are being generated correctly.

Usage:
    python test_signals.py

Author: AI Assistant
Date: 2025
"""

import sys
import os
import time
import json
from datetime import datetime

# Add src directory to path
sys.path.append('src')

from ema import EMACalculator
from strategy import EMAStrategy
from logger import SignalLogger
from market_feed import MockMarketFeed


def test_signal_generation():
    """Test signal generation with controlled price movements"""
    print("🧪 Testing EMA Signal Generation...")

    # Configuration
    ema_combinations = [
        {"short_ema": 5, "long_ema": 10}
    ]
    timeframes = ["1min"]

    # Create components
    ema_calculator = EMACalculator(ema_combinations)
    signal_logger = SignalLogger("test_data", timeframes, 100000)

    strategy = EMAStrategy(
        ema_combinations=ema_combinations,
        timeframes=timeframes,
        ema_calculator=ema_calculator,
        signal_logger=signal_logger
    )

    # Test with controlled price movements to force crossovers
    print("  Generating test price data to force EMA crossovers...")

    # Start with base price
    base_price = 19500.0

    # Generate prices that will create crossovers
    test_prices = [
        # Initial prices (trending down - should create death cross)
        19500, 19495, 19490, 19485, 19480, 19475, 19470, 19465, 19460, 19455,
        19450, 19445, 19440, 19435, 19430, 19425, 19420, 19415, 19410, 19405,

        # Reversal (trending up - should create golden cross)
        19410, 19415, 19420, 19425, 19430, 19435, 19440, 19445, 19450, 19455,
        19460, 19465, 19470, 19475, 19480, 19485, 19490, 19495, 19500, 19505,
        19510, 19515, 19520, 19525, 19530, 19535, 19540, 19545, 19550, 19555
    ]

    print(f"  Processing {len(test_prices)} price points...")

    # Start from a specific time and increment by 1 minute for each tick
    # This ensures each tick creates a new 1-minute candle
    base_time = time.time()

    for i, price in enumerate(test_prices):
        # Create tick data with timestamps 1 minute apart
        tick_data = {
            'timestamp': base_time + (i * 60),  # 60 seconds apart
            'price': price,
            'volume': 100,
            'security_id': '13',
            'exchange_segment': 'IDX_I'
        }

        # Process tick
        strategy.process_tick(tick_data)

        # Print progress
        if i % 10 == 0:
            print(f"    Processed {i+1}/{len(test_prices)} ticks, Price: {price}")

    # Force completion of any pending candles
    strategy.force_candle_completion()

    # Get statistics
    strategy_stats = strategy.get_statistics()
    logger_stats = signal_logger.get_statistics()

    print(f"\n📊 Results:")
    print(f"  Ticks processed: {strategy_stats['ticks_processed']}")
    print(f"  Candles generated: {dict(strategy_stats['candles_generated'])}")
    print(f"  Signals generated: {dict(strategy_stats['signals_generated'])}")
    print(f"  Total signals: {logger_stats['total_signals']}")

    # Check if signals were generated
    if logger_stats['total_signals'] > 0:
        print("✅ SUCCESS: Signals were generated!")

        # Show timeframe details
        for tf, stats in logger_stats['timeframes'].items():
            print(f"  {tf}: {stats['signal_count']} signals, P&L: {stats['cumulative_pnl']:.2f}")
    else:
        print("❌ ISSUE: No signals were generated")

        # Debug information
        print("\n🔍 Debug Information:")
        ema_stats = ema_calculator.get_statistics("1min")
        print(f"  EMA Calculator stats: {ema_stats}")

    # Close logger
    signal_logger.close()

    return logger_stats['total_signals'] > 0


def test_mock_feed():
    """Test the mock market feed"""
    print("\n🎭 Testing Mock Market Feed...")

    # Load configuration
    with open('config/config.json', 'r') as f:
        config = json.load(f)

    # Create components
    ema_calculator = EMACalculator(config['ema_combinations'])
    signal_logger = SignalLogger("test_data", ["1min"], 100000)

    strategy = EMAStrategy(
        ema_combinations=config['ema_combinations'],
        timeframes=["1min"],  # Just test 1min for speed
        ema_calculator=ema_calculator,
        signal_logger=signal_logger
    )

    # Create mock feed with faster tick rate
    mock_feed = MockMarketFeed(config['instrument'], strategy)
    mock_feed.tick_interval = 0.1  # 10 ticks per second

    print("  Starting mock feed for 10 seconds...")
    mock_feed.start()

    # Let it run for 10 seconds
    time.sleep(10)

    # Stop mock feed
    mock_feed.stop()

    # Get statistics
    strategy_stats = strategy.get_statistics()
    logger_stats = signal_logger.get_statistics()
    feed_stats = mock_feed.get_statistics()

    print(f"\n📊 Mock Feed Results:")
    print(f"  Ticks sent: {feed_stats['ticks_sent']}")
    print(f"  Ticks processed: {strategy_stats['ticks_processed']}")
    print(f"  Signals generated: {logger_stats['total_signals']}")

    # Close logger
    signal_logger.close()

    return feed_stats['ticks_sent'] > 0


def main():
    """Run signal generation tests"""
    print("=" * 60)
    print("EMA CROSSOVER SIGNAL GENERATION TEST")
    print("=" * 60)

    try:
        # Create test directories
        os.makedirs("test_data", exist_ok=True)

        # Test 1: Controlled signal generation
        success1 = test_signal_generation()

        # Test 2: Mock feed
        success2 = test_mock_feed()

        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 ALL TESTS PASSED!")
            print("\nThe EMA crossover system is working correctly.")
            print("You should now see signals when running the live system.")
        else:
            print("⚠️  SOME TESTS FAILED")
            if not success1:
                print("- Controlled signal generation failed")
            if not success2:
                print("- Mock feed test failed")

        print("\nNext steps:")
        print("1. Check test_data/ directory for generated CSV files")
        print("2. Run the live system: python src/main.py")
        print("3. Monitor logs for real-time signals")

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
