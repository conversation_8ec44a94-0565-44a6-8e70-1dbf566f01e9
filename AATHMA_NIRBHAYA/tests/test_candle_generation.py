#!/usr/bin/env python3
"""
Test Candle Generation
=====================

Test candle generation and EMA signal detection with controlled timestamps.
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append('src')

from ema import EMACalculator
from strategy import EMAStrategy
from logger import SignalLogger


def test_candle_generation_and_signals():
    """Test candle generation with controlled timestamps"""
    print("🧪 Testing Candle Generation and EMA Signals...")
    
    # Configuration
    ema_combinations = [{'short_ema': 5, 'long_ema': 10}]
    timeframes = ['1min']
    
    # Create components
    ema_calculator = EMACalculator(ema_combinations)
    signal_logger = SignalLogger("test_data", timeframes, 100000)
    
    strategy = EMAStrategy(
        ema_combinations=ema_combinations,
        timeframes=timeframes,
        ema_calculator=ema_calculator,
        signal_logger=signal_logger
    )
    
    # Generate test data with timestamps 1 minute apart
    base_time = datetime.now()
    
    # Create price pattern that will generate crossovers
    # Start high, trend down (death cross), then trend up (golden cross)
    test_prices = [
        # Initial high prices (trending down)
        19500, 19495, 19490, 19485, 19480, 19475, 19470, 19465, 19460, 19455,
        19450, 19445, 19440, 19435, 19430,
        # Reversal (trending up)
        19435, 19440, 19445, 19450, 19455, 19460, 19465, 19470, 19475, 19480,
        19485, 19490, 19495, 19500, 19505, 19510, 19515, 19520, 19525, 19530
    ]
    
    print(f"  Generating {len(test_prices)} ticks across {len(test_prices)} minutes...")
    
    signals_found = 0
    
    for i, price in enumerate(test_prices):
        # Create tick with timestamp i minutes from base_time
        tick_timestamp = base_time + timedelta(minutes=i)
        
        tick_data = {
            'timestamp': tick_timestamp,
            'price': price,
            'volume': 100,
            'security_id': '13',
            'exchange_segment': 'IDX_I'
        }
        
        # Process tick
        strategy.process_tick(tick_data)
        
        # Check for new signals after each tick
        current_stats = strategy.get_statistics()
        current_signals = sum(current_stats['signals_generated'].values())
        
        if current_signals > signals_found:
            signals_found = current_signals
            print(f"  🔔 Signal #{signals_found} generated at tick {i+1}, price {price}")
        
        # Print progress every 10 ticks
        if (i + 1) % 10 == 0:
            candles = sum(current_stats['candles_generated'].values())
            print(f"    Progress: {i+1}/{len(test_prices)} ticks, {candles} candles, {signals_found} signals")
    
    # Force completion of any pending candles
    strategy.force_candle_completion()
    
    # Get final statistics
    strategy_stats = strategy.get_statistics()
    logger_stats = signal_logger.get_statistics()
    
    print(f"\n📊 Final Results:")
    print(f"  Ticks processed: {strategy_stats['ticks_processed']}")
    print(f"  Candles generated: {dict(strategy_stats['candles_generated'])}")
    print(f"  Signals generated: {dict(strategy_stats['signals_generated'])}")
    print(f"  Total signals: {logger_stats['total_signals']}")
    
    # Analyze EMA status
    ema_stats = ema_calculator.get_statistics("1min")
    print(f"\n🔍 EMA Analysis:")
    print(f"  Price count: {ema_stats['price_count']}")
    print(f"  Latest price: {ema_stats['latest_price']}")
    print(f"  EMA status: {ema_stats['ema_status']}")
    
    # Check if we got the expected results
    success = True
    
    if strategy_stats['ticks_processed'] != len(test_prices):
        print(f"❌ Expected {len(test_prices)} ticks, got {strategy_stats['ticks_processed']}")
        success = False
    
    expected_candles = len(test_prices)  # Each tick should create a new candle
    actual_candles = sum(strategy_stats['candles_generated'].values())
    if actual_candles < expected_candles * 0.9:  # Allow some tolerance
        print(f"❌ Expected ~{expected_candles} candles, got {actual_candles}")
        success = False
    
    if logger_stats['total_signals'] == 0:
        print("❌ No signals generated")
        success = False
    else:
        print(f"✅ Generated {logger_stats['total_signals']} signals!")
        
        # Show signal details
        for tf, stats in logger_stats['timeframes'].items():
            if stats['signal_count'] > 0:
                print(f"  {tf}: {stats['signal_count']} signals, P&L: {stats['cumulative_pnl']:.2f}")
    
    # Close logger
    signal_logger.close()
    
    return success


def main():
    """Run the candle generation test"""
    print("=" * 60)
    print("TESTING CANDLE GENERATION AND EMA SIGNALS")
    print("=" * 60)
    
    try:
        # Create test directories
        os.makedirs("test_data", exist_ok=True)
        
        # Run test
        success = test_candle_generation_and_signals()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 SUCCESS! Candle generation and EMA signals are working!")
            print("\nThe system can now:")
            print("  ✅ Generate candles from tick data")
            print("  ✅ Calculate EMAs from candle close prices")
            print("  ✅ Detect EMA crossover signals")
            print("  ✅ Log signals to CSV files")
            print("\nYou can now run the live system with confidence!")
        else:
            print("⚠️  Some issues remain. Check the output above for details.")
        
        print("\nCheck test_data/ directory for generated CSV files.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
