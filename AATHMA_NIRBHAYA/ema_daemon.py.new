#!/usr/bin/env python3
"""
EMA Trading System Daemon
=========================

This script provides daemon-like functionality for the EMA trading system.
It can start, stop, restart, and check the status of the background process.

Usage:
    python ema_daemon.py start    # Start in background
    python ema_daemon.py stop     # Stop background process
    python ema_daemon.py restart  # Restart background process
    python ema_daemon.py status   # Check status
    python ema_daemon.py logs     # Show recent logs

Author: AI Assistant
Date: 2025
"""

import os
import sys
import time
import signal
import subprocess
import psutil
from datetime import datetime
from pathlib import Path


class EMADaemon:
    """Daemon manager for EMA trading system"""
    
    def __init__(self):
        # Get the absolute path to the project root directory
        self.root_dir = Path(__file__).parent.absolute()
        
        # Use absolute paths for all files
        self.pid_file = self.root_dir / "ema_system.pid"
        self.log_file = self.root_dir / "logs/ema_daemon.log"
        self.script_path = self.root_dir / "src/main.py"
        self.config_path = self.root_dir / "config/config.json"
        
        # Ensure logs directory exists
        self.log_file.parent.mkdir(exist_ok=True)
        
        # Set up environment variables path
        self.env_file = self.root_dir / ".env"
    
    def start(self):
        """Start the EMA system in background"""
        if self.is_running():
            print("❌ EMA system is already running")
            return False
        
        print("🚀 Starting EMA Trading System in background...")
        
        try:
            # Validate required files
            if not self.script_path.exists():
                print(f"❌ Main script not found: {self.script_path}")
                return False
                
            if not self.config_path.exists():
                print(f"❌ Configuration file not found: {self.config_path}")
                return False
            
            # Start the process in background
            with open(self.log_file, 'a') as log:
                # Set up environment
                env = os.environ.copy()
                env['PYTHONPATH'] = str(self.root_dir)
                
                # Start process with absolute paths
                process = subprocess.Popen([
                    sys.executable,
                    str(self.script_path),
                    '--background',
                    '--config', str(self.config_path)
                ], stdout=log, stderr=log, cwd=str(self.root_dir), env=env)
            
            # Save PID
            with open(self.pid_file, 'w') as f:
                f.write(str(process.pid))
            
            # Wait a moment to check if process started successfully
            time.sleep(2)
            
            if self.is_running():
                print(f"✅ EMA system started successfully (PID: {process.pid})")
                print(f"📝 Logs: {self.log_file}")
                print("💡 Use 'python ema_daemon.py logs' to monitor")
                return True
            else:
                print("❌ Failed to start EMA system")
                return False
                
        except Exception as e:
            print(f"❌ Error starting EMA system: {e}")
            return False
    
    def stop(self):
        """Stop the EMA system"""
        if not self.is_running():
            print("❌ EMA system is not running")
            return False
        
        try:
            pid = self.get_pid()
            if pid:
                print(f"🛑 Stopping EMA system (PID: {pid})...")
                
                try:
                    process = psutil.Process(pid)
                    
                    # Get all children before parent
                    children = process.children(recursive=True)
                    
                    # Stop children first
                    for child in children:
                        try:
                            child.terminate()
                        except psutil.NoSuchProcess:
                            pass
                    
                    # Stop parent
                    process.terminate()
                    
                    # Wait for processes to stop
                    gone, alive = psutil.wait_procs([process] + children, timeout=3)
                    
                    # Force kill if still running
                    for p in alive:
                        print(f"⚠️  Process {p.pid} didn't stop gracefully, force killing...")
                        try:
                            p.kill()
                        except psutil.NoSuchProcess:
                            pass
                    
                except psutil.NoSuchProcess:
                    print("⚠️  Process already terminated")
                
                # Clean up PID file
                if self.pid_file.exists():
                    self.pid_file.unlink()
                
                if not self.is_running():
                    print("✅ EMA system stopped successfully")
                    return True
                else:
                    print("❌ Failed to stop EMA system")
                    return False
                    
        except Exception as e:
            print(f"❌ Error stopping EMA system: {e}")
            return False
    
    def restart(self):
        """Restart the EMA system"""
        print("🔄 Restarting EMA Trading System...")
        self.stop()
        time.sleep(2)
        return self.start()
    
    def status(self):
        """Check status of EMA system"""
        if self.is_running():
            pid = self.get_pid()
            uptime = self.get_uptime()
            print(f"🟢 EMA system is RUNNING (PID: {pid})")
            print(f"⏱️  Uptime: {uptime}")
            
            # Show recent log entries
            if self.log_file.exists():
                print("\n📝 Recent log entries:")
                try:
                    subprocess.run(['tail', '-n', '5', str(self.log_file)])
                except Exception:
                    pass
        else:
            print("🔴 EMA system is NOT running")
            
            # Check if PID file exists but process is dead
            if self.pid_file.exists():
                print("⚠️  Stale PID file found, cleaning up...")
                self.pid_file.unlink()
    
    def logs(self, follow=False, lines=50):
        """Show logs"""
        if not self.log_file.exists():
            print("❌ No log file found")
            return
        
        try:
            if follow:
                # Follow logs (like tail -f)
                print(f"📝 Following logs from {self.log_file} (Ctrl+C to stop)...")
                subprocess.run(['tail', '-f', str(self.log_file)])
            else:
                # Show last N lines
                print(f"📝 Last {lines} lines from {self.log_file}:")
                print("-" * 60)
                subprocess.run(['tail', '-n', str(lines), str(self.log_file)])
                
        except KeyboardInterrupt:
            print("\n👋 Stopped following logs")
        except Exception as e:
            print(f"❌ Error reading logs: {e}")
    
    def is_running(self):
        """Check if EMA system is running"""
        pid = self.get_pid()
        if not pid:
            return False
        
        try:
            process = psutil.Process(pid)
            return process.is_running() and process.status() != psutil.STATUS_ZOMBIE
        except psutil.NoSuchProcess:
            return False
    
    def get_pid(self):
        """Get PID from PID file"""
        if not self.pid_file.exists():
            return None
        
        try:
            with open(self.pid_file, 'r') as f:
                return int(f.read().strip())
        except (ValueError, IOError):
            return None
    
    def get_uptime(self):
        """Get process uptime"""
        pid = self.get_pid()
        if not pid:
            return "Unknown"
        
        try:
            process = psutil.Process(pid)
            create_time = datetime.fromtimestamp(process.create_time())
            uptime = datetime.now() - create_time
            
            hours = int(uptime.total_seconds() // 3600)
            minutes = int((uptime.total_seconds() % 3600) // 60)
            seconds = int(uptime.total_seconds() % 60)
            
            return f"{hours}h {minutes}m {seconds}s"
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            return "Unknown"


def main():
    """Main entry point"""
    daemon = EMADaemon()
    
    if len(sys.argv) < 2:
        print("Usage: python ema_daemon.py {start|stop|restart|status|logs}")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == 'start':
        daemon.start()
    elif command == 'stop':
        daemon.stop()
    elif command == 'restart':
        daemon.restart()
    elif command == 'status':
        daemon.status()
    elif command == 'logs':
        follow = '--follow' in sys.argv or '-f' in sys.argv
        daemon.logs(follow=follow)
    else:
        print(f"❌ Unknown command: {command}")
        print("Available commands: start, stop, restart, status, logs")
        sys.exit(1)


if __name__ == "__main__":
    main()
