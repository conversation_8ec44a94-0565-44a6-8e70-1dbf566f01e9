# Development Dependencies
# NIFTY 50 EMA Crossover Trading System

# Include production dependencies
-r requirements.txt

# Testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0

# Code formatting and linting
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# Pre-commit hooks
pre-commit>=3.0.0

# Type checking
mypy>=1.5.0

# Documentation
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0

# Data analysis for development
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
jupyter>=1.0.0

# Performance profiling
memory-profiler>=0.61.0
line-profiler>=4.0.0

# Security scanning
bandit>=1.7.0
safety>=2.3.0
