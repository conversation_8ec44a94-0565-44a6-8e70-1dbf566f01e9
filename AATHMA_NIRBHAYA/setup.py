#!/usr/bin/env python3
"""
Setup script for NIFTY 50 EMA Crossover Trading System
"""

from setuptools import setup, find_packages
import os

# Read README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="nifty50-ema-trading",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="Professional-grade algorithmic trading system for NIFTY 50 EMA crossover strategies",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/nifty50-ema-trading",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "pre-commit>=3.0.0",
        ],
        "analysis": [
            "numpy>=1.24.0",
            "pandas>=2.0.0",
            "matplotlib>=3.7.0",
            "seaborn>=0.12.0",
            "jupyter>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ema-trading=src.main:main",
            "ema-daemon=ema_daemon:main",
            "ema-historical=manage_historical_data:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.conf", "*.md"],
    },
    keywords="trading, algorithmic, ema, crossover, nifty50, dhan, finance",
    project_urls={
        "Bug Reports": "https://github.com/your-username/nifty50-ema-trading/issues",
        "Source": "https://github.com/your-username/nifty50-ema-trading",
        "Documentation": "https://github.com/your-username/nifty50-ema-trading/docs",
    },
)
