#!/usr/bin/env python3
"""
Live Market Data Validation Script
==================================

This script fetches today's real NIFTY 50 data from DhanHQ market feed
and validates it against our system to identify any discrepancies.

Author: AI Assistant
Date: 2025
"""

import sys
import os
import json
from datetime import datetime, timedelta
import pandas as pd
import requests
from dotenv import load_dotenv

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def load_credentials():
    """Load DhanHQ credentials"""
    env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    load_dotenv(env_path)
    
    client_id = os.getenv('DHAN_CLIENT_ID')
    access_token = os.getenv('DHAN_ACCESS_TOKEN')
    
    if not client_id or not access_token:
        raise ValueError("DhanHQ credentials not found in .env file")
    
    return client_id, access_token

def fetch_todays_market_data(client_id, access_token):
    """Fetch today's complete NIFTY 50 data from DhanHQ"""
    print("📊 Fetching Today's Real Market Data from DhanHQ...")
    
    try:
        # Try using the official DhanHQ library first
        try:
            from dhanhq import dhanhq
            
            dhan = dhanhq(client_id, access_token)
            
            # Get today's date
            today = datetime.now().strftime("%Y-%m-%d")
            
            # Fetch historical data for today
            response = dhan.historical_daily_data(
                symbol="NIFTY 50",
                exchange_segment="IDX_I",
                instrument_type="INDEX",
                from_date=today,
                to_date=today
            )
            
            if response and 'data' in response:
                print(f"✅ Successfully fetched data using DhanHQ library")
                return response['data'], 'dhanhq_library'
            else:
                print(f"⚠️  DhanHQ library response: {response}")
                
        except Exception as e:
            print(f"⚠️  DhanHQ library failed: {e}")
        
        # Fallback to direct API calls
        print("🔄 Trying direct API calls...")
        
        # Use the same API endpoint as our historical database
        url = "https://api.dhan.co/charts/historical"
        headers = {
            'access-token': access_token,
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        # Get today's market hours
        today = datetime.now()
        market_start = datetime.combine(today.date(), datetime.strptime("09:15", "%H:%M").time())
        market_end = datetime.combine(today.date(), datetime.strptime("15:30", "%H:%M").time())
        
        payload = {
            "securityId": "13",  # NIFTY 50
            "exchangeSegment": "IDX_I",  # NSE Index
            "instrument": "INDEX",
            "fromDate": int(market_start.timestamp()),
            "toDate": int(market_end.timestamp()),
            "resolution": "1"  # 1-minute resolution
        }
        
        print(f"📅 Fetching data for: {today.strftime('%Y-%m-%d')}")
        print(f"⏰ Time range: {market_start.strftime('%H:%M')} - {market_end.strftime('%H:%M')}")
        
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        
        if response.status_code == 200:
            data = response.json()
            
            if data.get('status') == 'success':
                candles_data = data.get('data', {})
                
                # Extract OHLC data
                timestamps = candles_data.get('t', [])
                opens = candles_data.get('o', [])
                highs = candles_data.get('h', [])
                lows = candles_data.get('l', [])
                closes = candles_data.get('c', [])
                volumes = candles_data.get('v', [])
                
                if timestamps:
                    candles = []
                    for i in range(len(timestamps)):
                        candle_time = datetime.fromtimestamp(timestamps[i])
                        
                        candle = {
                            'timestamp': candle_time,
                            'open': float(opens[i]) if i < len(opens) else 0,
                            'high': float(highs[i]) if i < len(highs) else 0,
                            'low': float(lows[i]) if i < len(lows) else 0,
                            'close': float(closes[i]) if i < len(closes) else 0,
                            'volume': int(volumes[i]) if i < len(volumes) else 0
                        }
                        candles.append(candle)
                    
                    print(f"✅ Successfully fetched {len(candles)} candles via direct API")
                    return candles, 'direct_api'
                else:
                    print("❌ No timestamp data received")
                    return [], 'no_data'
            else:
                print(f"❌ API error: {data.get('message', 'Unknown error')}")
                return [], 'api_error'
        else:
            print(f"❌ HTTP error {response.status_code}: {response.text}")
            return [], 'http_error'
            
    except Exception as e:
        print(f"❌ Error fetching market data: {e}")
        return [], 'exception'

def load_system_data():
    """Load data from our system for comparison"""
    print("\n📂 Loading System Data for Comparison...")
    
    try:
        # Load today's signal file
        today = datetime.now().strftime("%Y%m%d")
        signal_file = os.path.join(os.path.dirname(__file__), '..', 'data', f'nifty50_ema_signals_{today}.csv')
        
        system_data = {}
        
        if os.path.exists(signal_file):
            df = pd.read_csv(signal_file)
            system_data['signals'] = df
            print(f"✅ Loaded {len(df)} signals from system")
        else:
            print(f"⚠️  No signal file found: {signal_file}")
            system_data['signals'] = pd.DataFrame()
        
        # Load historical database
        try:
            from data.historical_database import HistoricalDatabase
            
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.json')
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            db = HistoricalDatabase(
                market_hours_config=config.get('market_hours', {}),
                data_directory=config.get('data_directory', 'data')
            )
            
            # Get today's data from historical database
            database = db.load_database()
            today_str = datetime.now().strftime('%Y-%m-%d')
            
            if today_str in database:
                system_data['historical'] = database[today_str]
                print(f"✅ Loaded {len(database[today_str])} candles from historical database")
            else:
                print(f"⚠️  No data for {today_str} in historical database")
                system_data['historical'] = []
                
        except Exception as e:
            print(f"❌ Error loading historical database: {e}")
            system_data['historical'] = []
        
        return system_data
        
    except Exception as e:
        print(f"❌ Error loading system data: {e}")
        return {'signals': pd.DataFrame(), 'historical': []}

def compare_market_data(live_data, system_data):
    """Compare live market data with system data"""
    print("\n🔍 Comparing Live Market Data with System Data...")
    
    issues_found = []
    
    if not live_data:
        issues_found.append("❌ No live market data available for comparison")
        return issues_found
    
    # Convert live data to DataFrame for easier analysis
    live_df = pd.DataFrame(live_data)
    
    print(f"📊 Live Data Summary:")
    print(f"   Total Candles: {len(live_df)}")
    if len(live_df) > 0:
        print(f"   Time Range: {live_df['timestamp'].min()} to {live_df['timestamp'].max()}")
        print(f"   Price Range: ₹{live_df['close'].min():.2f} - ₹{live_df['close'].max():.2f}")
        print(f"   Latest Price: ₹{live_df['close'].iloc[-1]:.2f}")
    
    # Compare with historical database
    if system_data['historical']:
        hist_df = pd.DataFrame(system_data['historical'])
        
        print(f"\n📂 System Historical Data:")
        print(f"   Total Candles: {len(hist_df)}")
        if len(hist_df) > 0:
            print(f"   Price Range: ₹{hist_df['close'].min():.2f} - ₹{hist_df['close'].max():.2f}")
            print(f"   Latest Price: ₹{hist_df['close'].iloc[-1]:.2f}")
        
        # Check for data consistency
        if len(live_df) > 0 and len(hist_df) > 0:
            live_latest = live_df['close'].iloc[-1]
            hist_latest = hist_df['close'].iloc[-1]
            price_diff = abs(live_latest - hist_latest)
            
            if price_diff > 50:  # More than ₹50 difference
                issues_found.append(f"⚠️  Large price difference: Live ₹{live_latest:.2f} vs System ₹{hist_latest:.2f}")
            else:
                print(f"✅ Price consistency: Live ₹{live_latest:.2f} vs System ₹{hist_latest:.2f} (diff: ₹{price_diff:.2f})")
    
    # Analyze signal data
    if not system_data['signals'].empty:
        signals_df = system_data['signals']
        
        print(f"\n📈 System Signals Analysis:")
        print(f"   Total Signals: {len(signals_df)}")
        
        if 'Price' in signals_df.columns:
            signal_prices = pd.to_numeric(signals_df['Price'], errors='coerce')
            signal_prices = signal_prices.dropna()
            
            if len(signal_prices) > 0:
                print(f"   Signal Price Range: ₹{signal_prices.min():.2f} - ₹{signal_prices.max():.2f}")
                
                # Check if signal prices are within live data range
                if len(live_df) > 0:
                    live_min, live_max = live_df['close'].min(), live_df['close'].max()
                    
                    if signal_prices.min() < live_min - 100 or signal_prices.max() > live_max + 100:
                        issues_found.append(f"⚠️  Signal prices outside live data range")
                    else:
                        print(f"✅ Signal prices within expected range")
    
    return issues_found

def validate_ema_calculations(live_data):
    """Validate EMA calculations with live data"""
    print("\n🧮 Validating EMA Calculations with Live Data...")
    
    if not live_data or len(live_data) < 20:
        print("⚠️  Insufficient live data for EMA validation")
        return []
    
    try:
        from core.ema import EMACalculator
        
        # Extract prices from live data
        prices = [candle['close'] for candle in live_data]
        
        # Initialize EMA calculator
        ema_calculator = EMACalculator([{'short_ema': 5, 'long_ema': 10}])
        
        # Load prices and calculate EMAs
        ema_calculator.load_state_from_prices("1min", prices)
        
        # Get current EMA values
        current_emas = ema_calculator.get_current_ema_values("1min")
        
        if current_emas:
            print(f"✅ EMA Calculations with Live Data:")
            print(f"   EMA5: ₹{current_emas.get(5, 0):.2f}")
            print(f"   EMA10: ₹{current_emas.get(10, 0):.2f}")
            
            # Check for crossover signals
            signals = ema_calculator.get_crossover_signals("1min")
            if signals:
                print(f"   Recent Signals: {len(signals)} detected")
            else:
                print(f"   No recent crossover signals")
            
            return []
        else:
            return ["❌ Failed to calculate EMAs with live data"]
            
    except Exception as e:
        return [f"❌ EMA validation error: {e}"]

def main():
    """Main validation function"""
    print("🚀 Live Market Data Validation")
    print("=" * 60)
    
    try:
        # Load credentials
        client_id, access_token = load_credentials()
        print(f"✅ Credentials loaded: {client_id}")
        
        # Fetch live market data
        live_data, source = fetch_todays_market_data(client_id, access_token)
        print(f"📊 Data source: {source}")
        
        # Load system data
        system_data = load_system_data()
        
        # Compare data
        issues = compare_market_data(live_data, system_data)
        
        # Validate EMA calculations
        ema_issues = validate_ema_calculations(live_data)
        issues.extend(ema_issues)
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 VALIDATION SUMMARY")
        print("=" * 60)
        
        if not issues:
            print("✅ ALL VALIDATIONS PASSED")
            print("   - Live market data fetched successfully")
            print("   - System data is consistent with live data")
            print("   - EMA calculations working correctly")
            print("   - No issues detected")
        else:
            print(f"⚠️  {len(issues)} ISSUES FOUND:")
            for i, issue in enumerate(issues, 1):
                print(f"   {i}. {issue}")
        
        return len(issues) == 0
        
    except Exception as e:
        print(f"❌ VALIDATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
