#!/usr/bin/env python3
"""
DhanHQ Connection Test Script
============================

This script tests the DhanHQ connection and validates that we can fetch
real NIFTY 50 data from the DhanHQ API.

Author: AI Assistant
Date: 2025
"""

import sys
import os
import json
from datetime import datetime, timedelta
import requests

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def load_credentials():
    """Load DhanHQ credentials from environment"""
    from dotenv import load_dotenv

    # Load environment variables
    env_path = os.path.join(os.path.dirname(__file__), '..', '.env')
    load_dotenv(env_path)

    client_id = os.getenv('DHAN_CLIENT_ID')
    access_token = os.getenv('DHAN_ACCESS_TOKEN')

    if not client_id or not access_token:
        print("❌ DhanHQ credentials not found in .env file")
        return None, None

    print(f"✅ DhanHQ credentials loaded")
    print(f"   Client ID: {client_id}")
    print(f"   Access Token: {access_token[:20]}...")

    return client_id, access_token

def test_dhanhq_api_connection(client_id, access_token):
    """Test DhanHQ API connection using official library"""
    print("\n🔗 Testing DhanHQ API Connection...")

    try:
        # Try using the official DhanHQ library
        try:
            from dhanhq import dhanhq

            # Initialize DhanHQ client
            dhan = dhanhq(client_id, access_token)

            # Test a simple API call
            response = dhan.get_fund_limits()

            if response and 'status' in response:
                print("✅ DhanHQ API connection successful (using official library)")
                print(f"   Response status: {response.get('status', 'Unknown')}")
                return True
            else:
                print("⚠️  DhanHQ API connected but unexpected response format")
                print(f"   Response: {response}")
                return False

        except ImportError:
            print("⚠️  Official DhanHQ library not available, trying direct API calls...")

            # Fallback to direct API calls
            url = "https://api.dhan.co/funds"  # Try without v2
            headers = {
                'access-token': access_token,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                print("✅ DhanHQ API connection successful (direct API)")
                data = response.json()
                print(f"   Response status: {data.get('status', 'Unknown')}")
                return True
            else:
                print(f"❌ DhanHQ API connection failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False

    except Exception as e:
        print(f"❌ DhanHQ API connection error: {e}")
        return False

def test_nifty50_data_fetch(client_id, access_token):
    """Test fetching NIFTY 50 current data"""
    print("\n📊 Testing NIFTY 50 Data Fetch...")

    try:
        # Try using the official DhanHQ library first
        try:
            from dhanhq import dhanhq

            # Initialize DhanHQ client
            dhan = dhanhq(client_id, access_token)

            # Get historical data for NIFTY 50
            from_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            to_date = datetime.now().strftime("%Y-%m-%d")

            response = dhan.historical_minute_charts(
                symbol="NIFTY 50",
                exchange_segment="IDX_I",
                instrument_type="INDEX",
                from_date=from_date,
                to_date=to_date
            )

            if response and 'data' in response:
                candles = response['data']
                if candles:
                    latest_candle = candles[-1]
                    print("✅ NIFTY 50 data fetch successful (official library)")
                    print(f"   Latest price: {latest_candle.get('close', 'N/A')}")
                    print(f"   Timestamp: {latest_candle.get('timestamp', 'N/A')}")
                    print(f"   Total candles: {len(candles)}")
                    return True, candles
                else:
                    print("⚠️  No candle data received")
                    return False, []
            else:
                print(f"❌ API returned error: {response}")
                return False, []

        except ImportError:
            print("⚠️  Official DhanHQ library not available, trying direct API...")

            # Fallback to direct API calls
            url = "https://api.dhan.co/charts/historical"  # Try without v2
            headers = {
                'access-token': access_token,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            # NIFTY 50 parameters
            payload = {
                "securityId": "13",  # NIFTY 50
                "exchangeSegment": "IDX_I",  # NSE Index
                "instrument": "INDEX",
                "fromDate": (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d"),
                "toDate": datetime.now().strftime("%Y-%m-%d")
            }

            response = requests.post(url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                data = response.json()

                if data.get('status') == 'success' and 'data' in data:
                    candles = data['data']
                    if candles:
                        latest_candle = candles[-1]
                        print("✅ NIFTY 50 data fetch successful (direct API)")
                        print(f"   Latest price: {latest_candle.get('close', 'N/A')}")
                        print(f"   Timestamp: {latest_candle.get('timestamp', 'N/A')}")
                        print(f"   Total candles: {len(candles)}")
                        return True, candles
                    else:
                        print("⚠️  No candle data received")
                        return False, []
                else:
                    print(f"❌ API returned error: {data.get('message', 'Unknown error')}")
                    return False, []
            else:
                print(f"❌ NIFTY 50 data fetch failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False, []

    except Exception as e:
        print(f"❌ NIFTY 50 data fetch error: {e}")
        return False, []

def test_historical_database():
    """Test the historical database functionality"""
    print("\n🗄️  Testing Historical Database...")

    try:
        from data.historical_database import HistoricalDatabase

        # Load configuration
        config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.json')
        with open(config_path, 'r') as f:
            config = json.load(f)

        # Create historical database
        db = HistoricalDatabase(
            market_hours_config=config.get('market_hours', {}),
            data_directory=config.get('data_directory', 'data')
        )

        # Get database info
        db_info = db.get_database_info()
        print(f"✅ Historical database loaded")
        print(f"   Status: {db_info['status']}")
        print(f"   Total days: {db_info['total_days']}")
        print(f"   Total candles: {db_info['total_candles']:,}")

        # Get recent prices
        recent_prices = db.get_historical_prices(days=1)
        if recent_prices:
            print(f"   Recent prices: {len(recent_prices)} data points")
            print(f"   Latest price: {recent_prices[-1]:.2f}")
            print(f"   Price range: {min(recent_prices):.2f} - {max(recent_prices):.2f}")

        return True

    except Exception as e:
        print(f"❌ Historical database test failed: {e}")
        return False

def validate_current_signals():
    """Validate the current signal file data"""
    print("\n📈 Validating Current Signal Data...")

    try:
        # Check today's signal file
        today = datetime.now().strftime("%Y%m%d")
        signal_file = os.path.join(os.path.dirname(__file__), '..', 'data', f'nifty50_ema_signals_{today}.csv')

        if os.path.exists(signal_file):
            with open(signal_file, 'r') as f:
                lines = f.readlines()

            print(f"✅ Signal file found: {signal_file}")
            print(f"   Total signals: {len(lines) - 1}")  # Exclude header

            if len(lines) > 1:
                # Show recent signals
                print("   Recent signals:")
                for line in lines[-5:]:
                    if line.strip() and not line.startswith('Date'):
                        fields = line.strip().split(',')
                        if len(fields) >= 4:
                            print(f"     {fields[1]} - {fields[2]} @ ₹{fields[3]}")

                # Validate price ranges
                prices = []
                for line in lines[1:]:  # Skip header
                    if line.strip():
                        fields = line.strip().split(',')
                        if len(fields) >= 4:
                            try:
                                price = float(fields[3])
                                prices.append(price)
                            except ValueError:
                                pass

                if prices:
                    print(f"   Price range: ₹{min(prices):.2f} - ₹{max(prices):.2f}")
                    print(f"   Average price: ₹{sum(prices)/len(prices):.2f}")

                    # Check if prices are in realistic NIFTY 50 range
                    if 20000 <= min(prices) <= 30000 and 20000 <= max(prices) <= 30000:
                        print("✅ Prices are in realistic NIFTY 50 range")
                    else:
                        print("⚠️  Prices seem outside normal NIFTY 50 range")

            return True
        else:
            print(f"❌ No signal file found for today: {signal_file}")
            return False

    except Exception as e:
        print(f"❌ Signal validation failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 DhanHQ Connection and Data Validation Test")
    print("=" * 60)

    # Load credentials
    client_id, access_token = load_credentials()
    if not client_id or not access_token:
        print("❌ Cannot proceed without DhanHQ credentials")
        return False

    # Run tests
    tests_passed = 0
    total_tests = 4

    # Test 1: API Connection
    if test_dhanhq_api_connection(client_id, access_token):
        tests_passed += 1

    # Test 2: NIFTY 50 Data Fetch
    success, candles = test_nifty50_data_fetch(client_id, access_token)
    if success:
        tests_passed += 1

    # Test 3: Historical Database
    if test_historical_database():
        tests_passed += 1

    # Test 4: Signal Validation
    if validate_current_signals():
        tests_passed += 1

    # Summary
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")

    if tests_passed == total_tests:
        print("✅ ALL TESTS PASSED - DhanHQ connection and data are working correctly!")
        print("   The system is fetching real NIFTY 50 data from DhanHQ")
        print("   Historical data recovery is working with real market data")
    else:
        print("⚠️  Some tests failed - please check the issues above")

    return tests_passed == total_tests

if __name__ == "__main__":
    main()
