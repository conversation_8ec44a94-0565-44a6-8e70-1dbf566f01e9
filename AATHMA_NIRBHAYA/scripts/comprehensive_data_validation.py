#!/usr/bin/env python3
"""
Comprehensive Data Validation Script
====================================

This script performs a thorough validation of the NIFTY 50 EMA trading system
using real market data from DhanHQ and validates all components.

Author: AI Assistant
Date: 2025
"""

import sys
import os
import json
import pandas as pd
from datetime import datetime, timedelta
import numpy as np

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def validate_historical_data():
    """Validate the historical database contains real market data"""
    print("📊 Validating Historical Database...")

    try:
        # Load historical CSV data
        hist_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'historical', 'nifty50_historical.csv')

        if not os.path.exists(hist_file):
            return ["❌ Historical data file not found"]

        df = pd.read_csv(hist_file)

        print(f"✅ Historical data loaded: {len(df)} records")
        print(f"   Date range: {df['Date'].min()} to {df['Date'].max()}")
        print(f"   Price range: ₹{df['Close'].min():.2f} - ₹{df['Close'].max():.2f}")

        # Validate data quality
        issues = []

        # Check for realistic NIFTY 50 price ranges
        if df['Close'].min() < 15000 or df['Close'].max() > 35000:
            issues.append("⚠️  Prices outside realistic NIFTY 50 range")

        # Check for data completeness
        missing_data = df.isnull().sum().sum()
        if missing_data > 0:
            issues.append(f"⚠️  {missing_data} missing data points found")

        # Check for trading hours data
        df['Time'] = pd.to_datetime(df['Time'], format='%H:%M:%S').dt.time
        market_start = pd.to_datetime('09:15:00', format='%H:%M:%S').time()
        market_end = pd.to_datetime('15:30:00', format='%H:%M:%S').time()

        valid_hours = df[(df['Time'] >= market_start) & (df['Time'] <= market_end)]
        print(f"   Market hours data: {len(valid_hours)}/{len(df)} records ({len(valid_hours)/len(df)*100:.1f}%)")

        # Check for recent data
        latest_date = pd.to_datetime(df['Date'].max()).date()
        days_old = (datetime.now().date() - latest_date).days

        if days_old > 5:
            issues.append(f"⚠️  Data is {days_old} days old")
        else:
            print(f"✅ Data freshness: {days_old} days old")

        return issues

    except Exception as e:
        return [f"❌ Error validating historical data: {e}"]

def validate_signal_data():
    """Validate today's signal data"""
    print("\n📈 Validating Signal Data...")

    try:
        # Load today's signal file
        today = datetime.now().strftime("%Y%m%d")
        signal_file = os.path.join(os.path.dirname(__file__), '..', 'data', f'nifty50_ema_signals_{today}.csv')

        if not os.path.exists(signal_file):
            return [f"⚠️  No signal file found for today: {today}"]

        df = pd.read_csv(signal_file)

        print(f"✅ Signal data loaded: {len(df)} signals")

        issues = []

        if len(df) > 0:
            # Validate signal structure
            required_columns = ['Date', 'Time', 'Action', 'Price']
            missing_cols = [col for col in required_columns if col not in df.columns]
            if missing_cols:
                issues.append(f"❌ Missing columns: {missing_cols}")

            # Validate signal prices
            if 'Price' in df.columns:
                prices = pd.to_numeric(df['Price'], errors='coerce').dropna()
                if len(prices) > 0:
                    print(f"   Price range: ₹{prices.min():.2f} - ₹{prices.max():.2f}")
                    print(f"   Average price: ₹{prices.mean():.2f}")

                    # Check for realistic prices
                    if prices.min() < 20000 or prices.max() > 30000:
                        issues.append("⚠️  Signal prices outside realistic NIFTY 50 range")

            # Validate signal balance
            if 'Action' in df.columns:
                buy_signals = len(df[df['Action'] == 'BUY'])
                sell_signals = len(df[df['Action'] == 'SELL'])

                print(f"   Signal distribution: {buy_signals} BUY, {sell_signals} SELL")

                # Check for reasonable signal balance (should be roughly equal)
                if abs(buy_signals - sell_signals) > len(df) * 0.3:
                    issues.append("⚠️  Significant signal imbalance detected")

        return issues

    except Exception as e:
        return [f"❌ Error validating signal data: {e}"]

def validate_ema_calculations():
    """Validate EMA calculations with real data"""
    print("\n🧮 Validating EMA Calculations...")

    try:
        from core.ema import EMACalculator

        # Load recent historical data for testing
        hist_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'historical', 'nifty50_historical.csv')
        df = pd.read_csv(hist_file)

        # Get last 100 prices for testing
        recent_prices = df['Close'].tail(100).tolist()

        if len(recent_prices) < 20:
            return ["❌ Insufficient data for EMA validation"]

        # Test EMA calculator
        ema_calculator = EMACalculator([{'short_ema': 5, 'long_ema': 10}])
        ema_calculator.load_state_from_prices("1min", recent_prices)

        # Get current EMA values
        current_emas = ema_calculator.get_current_ema_values("1min")

        issues = []

        if current_emas:
            ema5 = current_emas.get(5)
            ema10 = current_emas.get(10)

            print(f"✅ EMA calculations successful:")
            if ema5 is not None:
                print(f"   EMA5: ₹{ema5:.2f}")
            if ema10 is not None:
                print(f"   EMA10: ₹{ema10:.2f}")
            print(f"   Latest price: ₹{recent_prices[-1]:.2f}")

            # Validate EMA values are reasonable
            latest_price = recent_prices[-1]

            if ema5 is not None and abs(ema5 - latest_price) > latest_price * 0.05:  # More than 5% difference
                issues.append("⚠️  EMA5 significantly different from current price")

            if ema10 is not None and abs(ema10 - latest_price) > latest_price * 0.1:  # More than 10% difference
                issues.append("⚠️  EMA10 significantly different from current price")

            if ema5 is not None and ema10 is not None and ema5 == ema10:
                issues.append("⚠️  EMA5 and EMA10 are identical")

            # Test crossover detection
            signals = ema_calculator.get_crossover_signals("1min")
            print(f"   Crossover signals: {len(signals)} detected")

        else:
            issues.append("❌ Failed to calculate EMAs")

        return issues

    except Exception as e:
        return [f"❌ Error validating EMA calculations: {e}"]

def validate_system_integration():
    """Validate system integration and configuration"""
    print("\n🔧 Validating System Integration...")

    issues = []

    try:
        # Check configuration file
        config_file = os.path.join(os.path.dirname(__file__), '..', 'config', 'config.json')
        if os.path.exists(config_file):
            with open(config_file, 'r') as f:
                config = json.load(f)

            print("✅ Configuration file loaded")

            # Validate key configuration elements
            if 'instrument' not in config:
                issues.append("❌ Missing instrument configuration")
            elif config['instrument'].get('security_id') != '13':
                issues.append("⚠️  Unexpected security ID (should be 13 for NIFTY 50)")

            if 'ema_combinations' not in config:
                issues.append("❌ Missing EMA combinations configuration")

        else:
            issues.append("❌ Configuration file not found")

        # Check credentials
        env_file = os.path.join(os.path.dirname(__file__), '..', '.env')
        if os.path.exists(env_file):
            print("✅ Environment file found")
        else:
            issues.append("⚠️  Environment file not found")

        # Check data directories
        data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
        if os.path.exists(data_dir):
            subdirs = ['historical', 'logs']
            for subdir in subdirs:
                subdir_path = os.path.join(data_dir, subdir)
                if os.path.exists(subdir_path):
                    print(f"✅ {subdir} directory exists")
                else:
                    issues.append(f"⚠️  {subdir} directory missing")
        else:
            issues.append("❌ Data directory not found")

        return issues

    except Exception as e:
        return [f"❌ Error validating system integration: {e}"]

def compare_with_market_reality():
    """Compare our data with known market reality"""
    print("\n🌍 Comparing with Market Reality...")

    try:
        # Load our historical data
        hist_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'historical', 'nifty50_historical.csv')
        df = pd.read_csv(hist_file)

        # Get latest data
        latest_data = df.tail(10)
        latest_price = df['Close'].iloc[-1]
        latest_date = df['Date'].iloc[-1]

        print(f"✅ Our latest data:")
        print(f"   Date: {latest_date}")
        print(f"   Price: ₹{latest_price:.2f}")

        issues = []

        # Check if price is in realistic range for NIFTY 50
        # NIFTY 50 typically trades between 15,000 - 30,000
        if 15000 <= latest_price <= 30000:
            print("✅ Price is in realistic NIFTY 50 range")
        else:
            issues.append(f"⚠️  Price ₹{latest_price:.2f} seems unrealistic for NIFTY 50")

        # Check for reasonable volatility
        recent_prices = df['Close'].tail(20)
        volatility = recent_prices.std() / recent_prices.mean() * 100

        print(f"   Recent volatility: {volatility:.2f}%")

        if volatility > 10:
            issues.append("⚠️  Unusually high volatility detected")
        elif volatility < 0.1:
            issues.append("⚠️  Unusually low volatility detected")

        # Check trading volume
        if 'Volume' in df.columns:
            avg_volume = df['Volume'].tail(20).mean()
            print(f"   Average volume: {avg_volume:.0f}")

            if avg_volume < 100:
                issues.append("⚠️  Unusually low trading volume")

        return issues

    except Exception as e:
        return [f"❌ Error comparing with market reality: {e}"]

def main():
    """Main validation function"""
    print("🚀 Comprehensive NIFTY 50 EMA System Validation")
    print("=" * 70)
    print("Validating all components with real market data...")
    print()

    all_issues = []

    # Run all validation tests
    tests = [
        ("Historical Data", validate_historical_data),
        ("Signal Data", validate_signal_data),
        ("EMA Calculations", validate_ema_calculations),
        ("System Integration", validate_system_integration),
        ("Market Reality Check", compare_with_market_reality)
    ]

    for test_name, test_func in tests:
        try:
            issues = test_func()
            all_issues.extend(issues)
        except Exception as e:
            all_issues.append(f"❌ {test_name} validation failed: {e}")

    # Summary
    print("\n" + "=" * 70)
    print("📋 COMPREHENSIVE VALIDATION SUMMARY")
    print("=" * 70)

    if not all_issues:
        print("🎉 ALL VALIDATIONS PASSED!")
        print()
        print("✅ CONFIRMED: System is using REAL NIFTY 50 market data")
        print("✅ CONFIRMED: EMA calculations are working correctly")
        print("✅ CONFIRMED: Signal generation is functioning properly")
        print("✅ CONFIRMED: All system components are integrated correctly")
        print()
        print("🚀 The AATHMA NIRBHAYA trading system is PRODUCTION READY!")

    else:
        print(f"⚠️  {len(all_issues)} ISSUES FOUND:")
        print()
        for i, issue in enumerate(all_issues, 1):
            print(f"   {i}. {issue}")

        # Categorize issues
        critical_issues = [issue for issue in all_issues if issue.startswith("❌")]
        warnings = [issue for issue in all_issues if issue.startswith("⚠️")]

        print(f"\n📊 Issue Summary:")
        print(f"   Critical Issues: {len(critical_issues)}")
        print(f"   Warnings: {len(warnings)}")

        if len(critical_issues) == 0:
            print("\n✅ No critical issues found - system is operational")
        else:
            print("\n❌ Critical issues need to be addressed")

    return len(all_issues) == 0

if __name__ == "__main__":
    main()
