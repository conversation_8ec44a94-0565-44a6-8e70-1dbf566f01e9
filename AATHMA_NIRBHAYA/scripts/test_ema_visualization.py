#!/usr/bin/env python3
"""
EMA Crossover Visualization Script
================================

Compares EMA calculation results with TradingView data and validates crossover signals
"""

import sys
import os
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import seaborn as sns

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.ema import EMACalculator

def load_signal_data():
    """Load actual signal data from CSV file"""
    try:
        # Try to load the most recent signal file
        data_files = [
            '../data/nifty50_ema_signals_20250530.csv',
            '../data/nifty50_ema_signals_20250529.csv'
        ]

        for file_path in data_files:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            if os.path.exists(full_path):
                df = pd.read_csv(full_path)
                print(f"✅ Loaded signal data from {file_path}")
                return df

        print("❌ No signal data files found")
        return None
    except Exception as e:
        print(f"❌ Error loading signal data: {e}")
        return None

def get_realistic_nifty_data():
    """Get realistic NIFTY 50 1-minute data for testing"""
    # Create more realistic price data with proper pre-market initialization
    base_time = datetime(2025, 5, 30, 9, 0)  # Start from 9:00 AM for pre-market data

    # Pre-market data (9:00 to 9:15) - 15 minutes
    premarket_times = [base_time + timedelta(minutes=i) for i in range(15)]
    premarket_prices = [
        24745, 24746, 24747, 24748, 24749, 24750, 24751, 24752, 24753, 24754,
        24755, 24756, 24757, 24758, 24759
    ]

    # Market open data (9:15 to 11:00) - 105 minutes
    market_open_time = datetime(2025, 5, 30, 9, 15)
    market_times = [market_open_time + timedelta(minutes=i) for i in range(105)]

    # More realistic price movement with volatility (105 data points to match market_times)
    market_prices = [
        # Initial movement (9:15-9:30) - 15 points
        24760, 24762, 24765, 24768, 24770, 24773, 24775, 24778, 24780, 24782,
        24785, 24787, 24790, 24788, 24786,
        # Downward trend (9:30-10:00) - 30 points
        24784, 24782, 24780, 24778, 24776, 24774, 24772, 24770, 24768, 24766,
        24764, 24762, 24760, 24758, 24756, 24754, 24752, 24750, 24748, 24746,
        24744, 24742, 24740, 24738, 24736, 24734, 24732, 24730, 24728, 24726,
        # Consolidation (10:00-10:30) - 30 points
        24724, 24722, 24720, 24722, 24724, 24726, 24728, 24730, 24732, 24734,
        24736, 24738, 24740, 24742, 24744, 24746, 24748, 24750, 24752, 24754,
        24756, 24758, 24760, 24762, 24764, 24766, 24768, 24770, 24772, 24774,
        # Recovery (10:30-11:00) - 30 points
        24776, 24778, 24780, 24782, 24784, 24786, 24788, 24790, 24792, 24794,
        24796, 24798, 24800, 24798, 24796, 24794, 24792, 24790, 24788, 24786,
        24784, 24782, 24780, 24778, 24776, 24774, 24772, 24770, 24768, 24766
    ]

    # Combine all data
    all_times = premarket_times + market_times
    all_prices = premarket_prices + market_prices

    return all_times, all_prices, premarket_prices, market_prices

def analyze_tradingview_pattern():
    """Analyze the pattern from TradingView image with proper pre-market data"""
    return get_realistic_nifty_data()

def test_ema_visualization():
    """Test and visualize EMA crossover signals with TradingView-compatible calculations"""

    # Load actual signal data
    signal_df = load_signal_data()

    # Get realistic NIFTY data with pre-market initialization
    all_times, all_prices, premarket_prices, market_prices = analyze_tradingview_pattern()

    # Configuration
    ema_combinations = [{'short_ema': 5, 'long_ema': 10}]

    # Create TradingView-compatible EMA calculator
    ema_calculator = EMACalculator(ema_combinations)

    print("🔄 Initializing EMA calculator with pre-market data...")

    # Load pre-market data for proper EMA initialization
    ema_calculator.load_premarket_data("1min", premarket_prices)

    print(f"✅ Pre-market data loaded: {len(premarket_prices)} data points")

    # Calculate EMAs for market data
    tv_ema5_values = []
    tv_ema10_values = []
    tv_signals = []
    market_times = all_times[len(premarket_prices):]  # Market times only

    print("🔄 Processing market data...")

    for i, price in enumerate(market_prices):
        emas = ema_calculator.add_price("1min", price)
        if emas:
            ema5_val = emas.get(5)
            ema10_val = emas.get(10)

            # Only append if both values are not None
            if ema5_val is not None and ema10_val is not None:
                tv_ema5_values.append(ema5_val)
                tv_ema10_values.append(ema10_val)

            # Check for signals
            new_signals = ema_calculator.get_crossover_signals("1min")
            if new_signals:
                for signal in new_signals:
                    tv_signals.append({
                        'timestamp': market_times[i],
                        'price': price,
                        'type': signal['signal']
                    })

    print(f"✅ Market data processed: {len(market_prices)} data points")
    print(f"📊 EMA values calculated: EMA5={len(tv_ema5_values)}, EMA10={len(tv_ema10_values)}")
    print(f"🎯 Crossover signals detected: {len(tv_signals)}")

    # Create comprehensive visualization
    fig, axes = plt.subplots(2, 2, figsize=(20, 12))
    fig.suptitle('NIFTY 50 EMA Crossover Analysis - TradingView Compatible', fontsize=16, fontweight='bold')

    # Plot 1: TradingView Pattern Recreation
    ax1 = axes[0, 0]
    ax1.plot(market_times, market_prices, label='Price', color='white', linewidth=2)
    if len(tv_ema5_values) > 0:
        ema_times = market_times[-len(tv_ema5_values):]
        ax1.plot(ema_times, tv_ema5_values, label='EMA 5', color='#00BFFF', linewidth=2)
    if len(tv_ema10_values) > 0:
        ema_times = market_times[-len(tv_ema10_values):]
        ax1.plot(ema_times, tv_ema10_values, label='EMA 10', color='#FFA500', linewidth=2)

    # Plot signals
    for signal in tv_signals:
        marker = '^' if signal['type'] == 'BUY' else 'v'
        color = '#00FF00' if signal['type'] == 'BUY' else '#FF0000'
        ax1.scatter(signal['timestamp'], signal['price'], marker=marker,
                   c=color, s=150, edgecolors='white', linewidth=1, zorder=5)

    ax1.set_title('TradingView Pattern Recreation', fontweight='bold')
    ax1.set_xlabel('Time')
    ax1.set_ylabel('Price')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.tick_params(axis='x', rotation=45)

    # Plot 2: Actual System Signals (if available)
    ax2 = axes[0, 1]
    if signal_df is not None and len(signal_df) > 0:
        # Convert time strings to datetime
        signal_df['DateTime'] = pd.to_datetime(signal_df['Date'] + ' ' + signal_df['Time'])

        # Plot price line
        ax2.plot(signal_df['DateTime'], signal_df['Price'], label='Price', color='white', linewidth=2)

        # Plot EMA values
        ax2.plot(signal_df['DateTime'], signal_df['EMA5_Value'], label='EMA 5', color='#00BFFF', linewidth=2)
        ax2.plot(signal_df['DateTime'], signal_df['EMA10_Value'], label='EMA 10', color='#FFA500', linewidth=2)

        # Plot signals
        buy_signals = signal_df[signal_df['Action'] == 'BUY']
        sell_signals = signal_df[signal_df['Action'] == 'SELL']

        ax2.scatter(buy_signals['DateTime'], buy_signals['Price'], marker='^',
                   c='#00FF00', s=150, edgecolors='white', linewidth=1, zorder=5, label='BUY')
        ax2.scatter(sell_signals['DateTime'], sell_signals['Price'], marker='v',
                   c='#FF0000', s=150, edgecolors='white', linewidth=1, zorder=5, label='SELL')

        ax2.set_title(f'Actual System Signals ({len(signal_df)} signals)', fontweight='bold')
    else:
        ax2.text(0.5, 0.5, 'No Signal Data Available', ha='center', va='center',
                transform=ax2.transAxes, fontsize=14)
        ax2.set_title('Actual System Signals', fontweight='bold')

    ax2.set_xlabel('Time')
    ax2.set_ylabel('Price')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.tick_params(axis='x', rotation=45)

    # Plot 3: Signal Analysis
    ax3 = axes[1, 0]
    if signal_df is not None and len(signal_df) > 0:
        # Count signals by hour
        signal_df['Hour'] = signal_df['DateTime'].dt.hour
        hourly_counts = signal_df.groupby(['Hour', 'Action']).size().unstack(fill_value=0)

        if 'BUY' in hourly_counts.columns and 'SELL' in hourly_counts.columns:
            hourly_counts.plot(kind='bar', ax=ax3, color=['#00FF00', '#FF0000'], alpha=0.7)
            ax3.set_title('Signal Distribution by Hour', fontweight='bold')
            ax3.set_xlabel('Hour of Day')
            ax3.set_ylabel('Number of Signals')
            ax3.tick_params(axis='x', rotation=0)
        else:
            ax3.text(0.5, 0.5, 'Insufficient Signal Data', ha='center', va='center',
                    transform=ax3.transAxes, fontsize=14)
    else:
        ax3.text(0.5, 0.5, 'No Signal Data Available', ha='center', va='center',
                transform=ax3.transAxes, fontsize=14)
        ax3.set_title('Signal Distribution Analysis', fontweight='bold')

    ax3.grid(True, alpha=0.3)

    # Plot 4: EMA Spread Analysis
    ax4 = axes[1, 1]

    # TradingView EMA spread
    if len(tv_ema5_values) > 0 and len(tv_ema10_values) > 0 and len(tv_ema5_values) == len(tv_ema10_values):
        tv_spread = np.array(tv_ema5_values) - np.array(tv_ema10_values)
        spread_times = market_times[-len(tv_spread):] if len(market_times) >= len(tv_spread) else market_times
        ax4.plot(spread_times, tv_spread, label='TradingView EMA Spread',
                color='#FFFF00', linewidth=2)
        ax4.axhline(y=0, color='white', linestyle='--', alpha=0.5)

    # Actual system EMA spread
    if signal_df is not None and len(signal_df) > 0:
        actual_spread = signal_df['EMA5_Value'] - signal_df['EMA10_Value']
        ax4.plot(signal_df['DateTime'], actual_spread, label='Actual System EMA Spread',
                color='#FF00FF', linewidth=2)

    ax4.set_title('EMA Spread Comparison', fontweight='bold')
    ax4.set_xlabel('Time')
    ax4.set_ylabel('EMA5 - EMA10')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    ax4.tick_params(axis='x', rotation=45)

    # Set dark background for all plots
    for ax in axes.flat:
        ax.set_facecolor('#1e1e1e')
        ax.tick_params(colors='white')
        ax.xaxis.label.set_color('white')
        ax.yaxis.label.set_color('white')
        ax.title.set_color('white')
        for spine in ax.spines.values():
            spine.set_color('white')

    fig.patch.set_facecolor('#2e2e2e')
    plt.tight_layout()

    # Save plots
    os.makedirs('test_data', exist_ok=True)
    plt.savefig('test_data/ema_comprehensive_analysis.png', dpi=300, bbox_inches='tight',
                facecolor='#2e2e2e', edgecolor='none')
    plt.show()

    # Print analysis summary
    print("\n" + "="*60)
    print("📊 EMA CROSSOVER ANALYSIS SUMMARY")
    print("="*60)

    print(f"\n🎯 TradingView-Compatible Analysis:")
    print(f"   • Price Range: {min(market_prices):.2f} - {max(market_prices):.2f}")
    print(f"   • Detected Signals: {len(tv_signals)}")
    if tv_signals:
        buy_count = sum(1 for s in tv_signals if s['type'] == 'BUY')
        sell_count = sum(1 for s in tv_signals if s['type'] == 'SELL')
        print(f"   • BUY Signals: {buy_count}")
        print(f"   • SELL Signals: {sell_count}")

    if signal_df is not None and len(signal_df) > 0:
        print(f"\n🤖 Actual System Performance:")
        print(f"   • Total Signals: {len(signal_df)}")
        print(f"   • BUY Signals: {len(signal_df[signal_df['Action'] == 'BUY'])}")
        print(f"   • SELL Signals: {len(signal_df[signal_df['Action'] == 'SELL'])}")
        print(f"   • Price Range: {signal_df['Price'].min():.2f} - {signal_df['Price'].max():.2f}")
        print(f"   • Time Range: {signal_df['DateTime'].min()} to {signal_df['DateTime'].max()}")

        # Calculate signal frequency
        time_span = (signal_df['DateTime'].max() - signal_df['DateTime'].min()).total_seconds() / 3600
        if time_span > 0:
            signal_frequency = len(signal_df) / time_span
            print(f"   • Signal Frequency: {signal_frequency:.2f} signals/hour")

    print(f"\n✅ Analysis completed successfully!")
    print(f"📈 Comprehensive chart saved as: test_data/ema_comprehensive_analysis.png")

if __name__ == "__main__":
    test_ema_visualization()
