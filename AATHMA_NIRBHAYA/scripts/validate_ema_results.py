#!/usr/bin/env python3
"""
EMA Results Validation Script
============================

Validates EMA crossover results against TradingView patterns and provides detailed analysis
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def load_and_analyze_signals():
    """Load and analyze the actual signal data"""
    try:
        # Load the signal data
        data_file = '../data/nifty50_ema_signals_20250530.csv'
        full_path = os.path.join(os.path.dirname(__file__), data_file)
        
        if not os.path.exists(full_path):
            print("❌ Signal data file not found")
            return None
            
        df = pd.read_csv(full_path)
        df['DateTime'] = pd.to_datetime(df['Date'] + ' ' + df['Time'])
        
        print(f"✅ Loaded {len(df)} signals from {data_file}")
        return df
        
    except Exception as e:
        print(f"❌ Error loading signal data: {e}")
        return None

def analyze_tradingview_comparison(df):
    """Compare our results with TradingView pattern expectations"""
    
    print("\n" + "="*70)
    print("🔍 DETAILED VALIDATION ANALYSIS")
    print("="*70)
    
    if df is None or len(df) == 0:
        print("❌ No data available for analysis")
        return
    
    # Time-based analysis
    print(f"\n📅 TIME ANALYSIS:")
    print(f"   • Trading Session: {df['DateTime'].min()} to {df['DateTime'].max()}")
    
    # Calculate session duration
    session_duration = (df['DateTime'].max() - df['DateTime'].min()).total_seconds() / 3600
    print(f"   • Session Duration: {session_duration:.1f} hours")
    
    # Signal frequency analysis
    signal_frequency = len(df) / session_duration if session_duration > 0 else 0
    print(f"   • Signal Frequency: {signal_frequency:.2f} signals/hour")
    
    # Price movement analysis
    print(f"\n📈 PRICE MOVEMENT ANALYSIS:")
    price_range = df['Price'].max() - df['Price'].min()
    print(f"   • Price Range: {df['Price'].min():.2f} - {df['Price'].max():.2f}")
    print(f"   • Total Range: {price_range:.2f} points")
    print(f"   • Average Price: {df['Price'].mean():.2f}")
    
    # EMA analysis
    print(f"\n🧮 EMA ANALYSIS:")
    ema5_range = df['EMA5_Value'].max() - df['EMA5_Value'].min()
    ema10_range = df['EMA10_Value'].max() - df['EMA10_Value'].min()
    print(f"   • EMA5 Range: {df['EMA5_Value'].min():.2f} - {df['EMA5_Value'].max():.2f} ({ema5_range:.2f})")
    print(f"   • EMA10 Range: {df['EMA10_Value'].min():.2f} - {df['EMA10_Value'].max():.2f} ({ema10_range:.2f})")
    
    # Signal distribution
    buy_signals = len(df[df['Action'] == 'BUY'])
    sell_signals = len(df[df['Action'] == 'SELL'])
    print(f"\n📊 SIGNAL DISTRIBUTION:")
    print(f"   • BUY Signals: {buy_signals} ({buy_signals/len(df)*100:.1f}%)")
    print(f"   • SELL Signals: {sell_signals} ({sell_signals/len(df)*100:.1f}%)")
    print(f"   • Signal Balance: {'✅ Balanced' if abs(buy_signals - sell_signals) <= 2 else '⚠️ Imbalanced'}")
    
    # Hourly distribution
    df['Hour'] = df['DateTime'].dt.hour
    hourly_dist = df.groupby('Hour').size()
    print(f"\n⏰ HOURLY SIGNAL DISTRIBUTION:")
    for hour, count in hourly_dist.items():
        print(f"   • {hour:02d}:00 - {count} signals")
    
    # Most active hours
    most_active_hour = hourly_dist.idxmax()
    print(f"   • Most Active Hour: {most_active_hour:02d}:00 ({hourly_dist.max()} signals)")
    
    # EMA crossover validation
    print(f"\n🔄 EMA CROSSOVER VALIDATION:")
    
    # Calculate EMA spread
    df['EMA_Spread'] = df['EMA5_Value'] - df['EMA10_Value']
    
    # Check for proper crossovers
    crossover_validation = []
    for i in range(1, len(df)):
        prev_spread = df.iloc[i-1]['EMA_Spread']
        curr_spread = df.iloc[i]['EMA_Spread']
        action = df.iloc[i]['Action']
        
        # BUY signal should occur when EMA5 crosses above EMA10 (spread goes from negative to positive)
        # SELL signal should occur when EMA5 crosses below EMA10 (spread goes from positive to negative)
        
        if action == 'BUY':
            valid = prev_spread <= 0 and curr_spread > 0
        else:  # SELL
            valid = prev_spread >= 0 and curr_spread < 0
            
        crossover_validation.append(valid)
    
    valid_crossovers = sum(crossover_validation)
    total_crossovers = len(crossover_validation)
    validation_rate = (valid_crossovers / total_crossovers * 100) if total_crossovers > 0 else 0
    
    print(f"   • Valid Crossovers: {valid_crossovers}/{total_crossovers} ({validation_rate:.1f}%)")
    print(f"   • Crossover Quality: {'✅ Excellent' if validation_rate >= 90 else '⚠️ Needs Review' if validation_rate >= 70 else '❌ Poor'}")

def compare_with_tradingview_pattern():
    """Compare with the expected TradingView pattern"""
    
    print(f"\n🎯 TRADINGVIEW PATTERN COMPARISON:")
    print(f"   Based on the provided TradingView chart:")
    
    # Expected characteristics from the TradingView image
    expected_characteristics = {
        'time_range': '09:30 - 10:45 (75 minutes)',
        'price_range': '24,720 - 24,780 (60 points)',
        'pattern': 'Initial rise then decline',
        'ema_behavior': 'EMA5 (blue) and EMA10 (orange) crossovers',
        'expected_signals': '2-4 crossover signals in the timeframe'
    }
    
    print(f"   • Expected Time Range: {expected_characteristics['time_range']}")
    print(f"   • Expected Price Range: {expected_characteristics['price_range']}")
    print(f"   • Expected Pattern: {expected_characteristics['pattern']}")
    print(f"   • Expected Signals: {expected_characteristics['expected_signals']}")
    
    # Load our data for comparison
    df = load_and_analyze_signals()
    if df is not None:
        # Filter data for the TradingView timeframe (09:30-10:45)
        tv_start = df['DateTime'].dt.normalize() + pd.Timedelta(hours=9, minutes=30)
        tv_end = df['DateTime'].dt.normalize() + pd.Timedelta(hours=10, minutes=45)
        
        tv_period_data = df[(df['DateTime'] >= tv_start.iloc[0]) & (df['DateTime'] <= tv_end.iloc[0])]
        
        print(f"\n📊 OUR SYSTEM vs TRADINGVIEW (09:30-10:45 period):")
        if len(tv_period_data) > 0:
            tv_price_range = tv_period_data['Price'].max() - tv_period_data['Price'].min()
            print(f"   • Our Price Range: {tv_period_data['Price'].min():.2f} - {tv_period_data['Price'].max():.2f} ({tv_price_range:.2f} points)")
            print(f"   • Our Signals in Period: {len(tv_period_data)}")
            print(f"   • Signal Types: {len(tv_period_data[tv_period_data['Action'] == 'BUY'])} BUY, {len(tv_period_data[tv_period_data['Action'] == 'SELL'])} SELL")
        else:
            print(f"   • No signals found in TradingView timeframe")

def generate_recommendations():
    """Generate recommendations based on the analysis"""
    
    print(f"\n💡 RECOMMENDATIONS:")
    
    df = load_and_analyze_signals()
    if df is None:
        print("   • Cannot generate recommendations without data")
        return
    
    # Calculate signal frequency
    session_duration = (df['DateTime'].max() - df['DateTime'].min()).total_seconds() / 3600
    signal_frequency = len(df) / session_duration if session_duration > 0 else 0
    
    if signal_frequency > 5:
        print("   ⚠️  High signal frequency detected - consider increasing EMA periods or adding filters")
    elif signal_frequency < 1:
        print("   ⚠️  Low signal frequency detected - consider decreasing EMA periods")
    else:
        print("   ✅ Signal frequency appears optimal")
    
    # Check signal balance
    buy_signals = len(df[df['Action'] == 'BUY'])
    sell_signals = len(df[df['Action'] == 'SELL'])
    
    if abs(buy_signals - sell_signals) > 5:
        print("   ⚠️  Signal imbalance detected - review market conditions or EMA parameters")
    else:
        print("   ✅ Signal distribution is balanced")
    
    # Price range analysis
    price_range = df['Price'].max() - df['Price'].min()
    if price_range > 1000:
        print("   ⚠️  Large price range detected - consider position sizing adjustments")
    else:
        print("   ✅ Price range is manageable")

def main():
    """Main validation function"""
    print("🔍 EMA CROSSOVER RESULTS VALIDATION")
    print("="*50)
    
    # Load and analyze signals
    df = load_and_analyze_signals()
    
    # Perform detailed analysis
    analyze_tradingview_comparison(df)
    
    # Compare with TradingView pattern
    compare_with_tradingview_pattern()
    
    # Generate recommendations
    generate_recommendations()
    
    print(f"\n✅ Validation completed!")
    print(f"📊 View the comprehensive chart: test_data/ema_comprehensive_analysis.png")

if __name__ == "__main__":
    main()
