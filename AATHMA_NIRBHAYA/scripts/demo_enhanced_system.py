#!/usr/bin/env python3
"""
Enhanced EMA System Demo
========================

Demonstrates the complete enhanced EMA crossover system with:
- Market hours awareness
- State persistence across restarts
- Daily CSV file management
- Background mode operation

This demo simulates a full trading day with system restarts.
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta

# Add src directory to path
sys.path.append('src')

from ema import EMACalculator
from strategy import EMAStrategy
from logger import SignalLogger
from state_manager import StateManager
from market_hours import MarketHoursManager


def simulate_trading_session(session_name, prices, start_time_offset=0):
    """Simulate a trading session with given prices"""
    print(f"\n📊 {session_name}")
    print("-" * 40)
    
    # Create fresh components (simulating system restart)
    state_manager = StateManager("demo_data")
    ema_calculator = EMACalculator([{'short_ema': 5, 'long_ema': 10}])
    signal_logger = SignalLogger("demo_data", 100000)
    
    strategy = EMAStrategy(
        ema_combinations=[{'short_ema': 5, 'long_ema': 10}],
        timeframes=['1min'],
        ema_calculator=ema_calculator,
        signal_logger=signal_logger,
        state_manager=state_manager
    )
    
    # Load existing state (if any)
    print("📂 Loading previous state...")
    state_loaded = strategy.load_historical_state()
    
    if state_loaded:
        ema_values = ema_calculator.get_current_ema_values('1min')
        logger_stats = signal_logger.get_statistics()
        print(f"  ✅ State loaded: EMA5={ema_values.get('EMA5', 0):.2f}, EMA10={ema_values.get('EMA10', 0):.2f}")
        print(f"  📈 Existing signals: {logger_stats['total_signals']}, P&L: {logger_stats['cumulative_pnl']:.2f}")
    else:
        print("  🆕 Starting fresh (no previous state)")
    
    # Process new tick data
    base_time = datetime.now() + timedelta(hours=start_time_offset)
    signals_before = signal_logger.get_statistics()['total_signals']
    
    print(f"📈 Processing {len(prices)} new price points...")
    
    for i, price in enumerate(prices):
        tick_data = {
            'timestamp': base_time + timedelta(minutes=i),
            'price': price,
            'volume': 100,
            'security_id': '13',
            'exchange_segment': 'IDX_I'
        }
        strategy.process_tick(tick_data)
        
        # Show progress
        if (i + 1) % 5 == 0:
            current_stats = strategy.get_statistics()
            current_signals = signal_logger.get_statistics()['total_signals']
            new_signals = current_signals - signals_before
            print(f"  Processed {i+1}/{len(prices)} ticks, {new_signals} new signals")
    
    # Complete session
    strategy.force_candle_completion()
    state_manager.save_daily_state()
    
    # Show session results
    final_stats = strategy.get_statistics()
    final_logger_stats = signal_logger.get_statistics()
    final_ema_values = ema_calculator.get_current_ema_values('1min')
    
    print(f"\n📊 Session Results:")
    print(f"  Candles processed: {sum(final_stats['candles_generated'].values())}")
    print(f"  Total signals: {final_logger_stats['total_signals']}")
    print(f"  Session P&L: {final_logger_stats['cumulative_pnl']:.2f}")
    print(f"  Final EMAs: EMA5={final_ema_values.get('EMA5', 0):.2f}, EMA10={final_ema_values.get('EMA10', 0):.2f}")
    
    # Close session
    signal_logger.close()
    
    return final_logger_stats['total_signals'], final_logger_stats['cumulative_pnl']


def demo_market_hours():
    """Demo market hours functionality"""
    print("🕐 Market Hours Demo")
    print("-" * 40)
    
    market_config = {
        'timezone': 'Asia/Kolkata',
        'start_time': '09:15',
        'end_time': '15:15',
        'trading_days': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday']
    }
    
    manager = MarketHoursManager(market_config, "demo_data")
    
    current_time = manager.get_current_time()
    session_info = manager.get_market_session_info()
    
    print(f"Current time (IST): {current_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
    print(f"Market status: {manager.get_market_status_string()}")
    print(f"Is trading day: {session_info['is_trading_day']}")
    print(f"Is market open: {session_info['is_market_open']}")
    
    if session_info['is_market_open']:
        time_to_close = manager.get_time_until_market_close()
        if time_to_close:
            hours = time_to_close // 3600
            minutes = (time_to_close % 3600) // 60
            print(f"Time until close: {hours}h {minutes}m")


def demo_daily_csv():
    """Demo daily CSV file management"""
    print("\n📄 Daily CSV Demo")
    print("-" * 40)
    
    today = datetime.now().strftime("%Y%m%d")
    csv_file = f"demo_data/nifty50_ema_signals_{today}.csv"
    
    if os.path.exists(csv_file):
        with open(csv_file, 'r') as f:
            lines = f.readlines()
        
        print(f"Today's CSV file: {csv_file}")
        print(f"Total records: {len(lines) - 1} (excluding header)")
        
        if len(lines) > 1:
            print("\nRecent signals:")
            print("Time      | Action | Price    | EMA5     | EMA10    | P&L")
            print("-" * 60)
            
            for line in lines[-5:]:  # Last 5 signals
                if line.strip() and not line.startswith('Date'):
                    fields = line.strip().split(',')
                    if len(fields) >= 7:
                        time_str = fields[1]
                        action = fields[2]
                        price = fields[3]
                        ema5 = fields[4]
                        ema10 = fields[5]
                        pnl = fields[6]
                        print(f"{time_str} | {action:6} | {price:8} | {ema5:8} | {ema10:8} | {pnl:6}")
    else:
        print(f"No CSV file found for today: {csv_file}")


def main():
    """Run the enhanced system demo"""
    print("=" * 60)
    print("ENHANCED NIFTY 50 EMA CROSSOVER SYSTEM DEMO")
    print("=" * 60)
    
    try:
        # Create demo directories
        os.makedirs("demo_data", exist_ok=True)
        
        # Demo 1: Market Hours
        demo_market_hours()
        
        # Demo 2: Simulate morning trading session
        morning_prices = [
            19500, 19495, 19490, 19485, 19480,  # Downtrend
            19475, 19470, 19465, 19460, 19455   # Continued down
        ]
        
        total_signals, total_pnl = simulate_trading_session(
            "Morning Session (9:30 AM - 10:30 AM)", 
            morning_prices, 
            start_time_offset=0
        )
        
        print("\n⏸️  Simulating system restart...")
        time.sleep(1)
        
        # Demo 3: Simulate afternoon trading session (after restart)
        afternoon_prices = [
            19460, 19465, 19470, 19475, 19480,  # Reversal uptrend
            19485, 19490, 19495, 19500, 19505   # Continued up
        ]
        
        total_signals, total_pnl = simulate_trading_session(
            "Afternoon Session (1:00 PM - 2:00 PM) - After Restart", 
            afternoon_prices, 
            start_time_offset=4
        )
        
        # Demo 4: Show daily CSV results
        demo_daily_csv()
        
        # Summary
        print("\n" + "=" * 60)
        print("🎉 DEMO COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        print("\nKey Features Demonstrated:")
        print("  ✅ Market hours awareness (9:15 AM - 3:15 PM IST)")
        print("  ✅ EMA state persistence across system restarts")
        print("  ✅ Continuous signal generation within trading day")
        print("  ✅ Single daily CSV file with all crossovers")
        print("  ✅ P&L tracking across sessions")
        print("  ✅ 5/10 EMA crossover signal detection")
        
        print(f"\n📊 Final Results:")
        print(f"  Total signals generated: {total_signals}")
        print(f"  Final P&L: ₹{total_pnl:.2f}")
        print(f"  CSV file: demo_data/nifty50_ema_signals_{datetime.now().strftime('%Y%m%d')}.csv")
        
        print("\n🚀 Production Usage:")
        print("  python src/main.py --background    # Background mode")
        print("  python ema_daemon.py start         # Daemon mode")
        print("  python ema_daemon.py status        # Check status")
        print("  python ema_daemon.py logs          # View logs")
        
        print("\n💡 The system is now ready for live NIFTY 50 trading!")
        print("   It will automatically handle market hours, maintain EMA state,")
        print("   and generate crossover signals throughout the trading day.")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
