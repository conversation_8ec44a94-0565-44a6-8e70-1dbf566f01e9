#!/usr/bin/env python3
"""
Generate and Validate Today's Data Script
=========================================

This script generates realistic 1-minute NIFTY 50 price data for today
and validates our EMA strategy with this data.

Author: AI Assistant
Date: 2025
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import matplotlib.pyplot as plt

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

def generate_realistic_nifty_data():
    """Generate realistic 1-minute NIFTY 50 data for today"""
    print("📊 Generating Realistic NIFTY 50 Data for Today...")

    # Market parameters
    market_date = datetime.now().strftime("%Y-%m-%d")
    market_start = datetime.combine(datetime.now().date(), time(9, 15))
    market_end = datetime.combine(datetime.now().date(), time(15, 30))

    # Generate minute-by-minute timestamps
    timestamps = []
    current_time = market_start
    while current_time <= market_end:
        timestamps.append(current_time)
        current_time += timedelta(minutes=1)

    print(f"   Market session: {market_start.strftime('%H:%M')} - {market_end.strftime('%H:%M')}")
    print(f"   Total candles: {len(timestamps)}")

    # Starting price based on yesterday's close (₹23,926.79) with gap up
    opening_price = 24750.0  # Realistic gap up opening

    # Generate realistic price movement
    prices = []
    volumes = []

    # Market phases with different characteristics (more realistic)
    phases = [
        {"name": "Opening Rally", "duration": 30, "trend": 0.0008, "volatility": 0.0003},
        {"name": "Morning Consolidation", "duration": 60, "trend": 0.0002, "volatility": 0.0002},
        {"name": "Mid-Morning Dip", "duration": 45, "trend": -0.0006, "volatility": 0.0004},
        {"name": "Recovery", "duration": 60, "trend": 0.0005, "volatility": 0.0003},
        {"name": "Lunch Consolidation", "duration": 90, "trend": 0.0001, "volatility": 0.0001},
        {"name": "Afternoon Volatility", "duration": 60, "trend": -0.0003, "volatility": 0.0005},
        {"name": "Closing Rally", "duration": 30, "trend": 0.0004, "volatility": 0.0002}
    ]

    current_price = opening_price
    candle_index = 0

    for phase in phases:
        phase_duration = min(phase["duration"], len(timestamps) - candle_index)

        for i in range(phase_duration):
            if candle_index >= len(timestamps):
                break

            # Calculate price movement (more realistic)
            trend_component = phase["trend"] * np.random.normal(1.0, 0.5)
            random_component = np.random.normal(0, phase["volatility"])
            noise_component = np.random.normal(0, 0.0001)

            # Price change (smaller, more realistic movements)
            price_change = current_price * (trend_component + random_component + noise_component)
            new_price = current_price + price_change

            # Ensure realistic bounds (tighter range)
            new_price = max(current_price * 0.998, min(current_price * 1.002, new_price))

            # Generate OHLC for this minute
            high = new_price + abs(np.random.normal(0, current_price * 0.0005))
            low = new_price - abs(np.random.normal(0, current_price * 0.0005))
            open_price = current_price
            close_price = new_price

            # Ensure OHLC logic
            high = max(high, open_price, close_price)
            low = min(low, open_price, close_price)

            # Generate realistic volume
            base_volume = 2000
            volume_multiplier = 1 + abs(price_change / current_price) * 10  # Higher volume on big moves
            volume = int(base_volume * volume_multiplier * np.random.uniform(0.5, 2.0))

            prices.append({
                'timestamp': timestamps[candle_index],
                'date': market_date,
                'time': timestamps[candle_index].strftime('%H:%M:%S'),
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close_price, 2),
                'volume': volume
            })

            current_price = close_price
            candle_index += 1

    # Fill remaining candles if any
    while candle_index < len(timestamps):
        # Gentle drift for remaining time
        price_change = current_price * np.random.normal(0, 0.001)
        new_price = current_price + price_change
        new_price = max(24000, min(26000, new_price))

        high = new_price + abs(np.random.normal(0, current_price * 0.0003))
        low = new_price - abs(np.random.normal(0, current_price * 0.0003))

        high = max(high, current_price, new_price)
        low = min(low, current_price, new_price)

        prices.append({
            'timestamp': timestamps[candle_index],
            'date': market_date,
            'time': timestamps[candle_index].strftime('%H:%M:%S'),
            'open': round(current_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(new_price, 2),
            'volume': int(np.random.uniform(1000, 3000))
        })

        current_price = new_price
        candle_index += 1

    print(f"✅ Generated {len(prices)} realistic candles")
    print(f"   Opening: ₹{prices[0]['close']:.2f}")
    print(f"   Closing: ₹{prices[-1]['close']:.2f}")
    print(f"   High: ₹{max(p['high'] for p in prices):.2f}")
    print(f"   Low: ₹{min(p['low'] for p in prices):.2f}")

    return prices

def validate_ema_strategy(price_data):
    """Validate EMA strategy with generated data"""
    print("\n🧮 Validating EMA Strategy with Generated Data...")

    try:
        from core.ema import EMACalculator

        # Extract close prices
        close_prices = [candle['close'] for candle in price_data]
        timestamps = [candle['timestamp'] for candle in price_data]

        # Initialize EMA calculator with 5/10 crossover strategy
        ema_calculator = EMACalculator([{'short_ema': 5, 'long_ema': 10}])

        # Process data and collect results
        ema5_values = []
        ema10_values = []
        signals = []

        print(f"   Processing {len(close_prices)} price points...")

        for i, price in enumerate(close_prices):
            # Add price to calculator
            emas = ema_calculator.add_price("1min", price)

            # Store EMA values
            if 5 in emas and 10 in emas:
                ema5_values.append(emas[5])
                ema10_values.append(emas[10])
            else:
                ema5_values.append(None)
                ema10_values.append(None)

            # Check for crossover signals
            new_signals = ema_calculator.get_crossover_signals("1min")
            for signal in new_signals:
                signals.append({
                    'timestamp': timestamps[i],
                    'price': price,
                    'signal': signal['signal'],
                    'ema5': signal['short_value'],
                    'ema10': signal['long_value']
                })

        print(f"✅ EMA calculation completed")
        print(f"   EMA5 values calculated: {sum(1 for x in ema5_values if x is not None)}")
        print(f"   EMA10 values calculated: {sum(1 for x in ema10_values if x is not None)}")
        print(f"   Crossover signals detected: {len(signals)}")

        # Analyze signals
        if signals:
            buy_signals = [s for s in signals if s['signal'] == 'BUY']
            sell_signals = [s for s in signals if s['signal'] == 'SELL']

            print(f"   BUY signals: {len(buy_signals)}")
            print(f"   SELL signals: {len(sell_signals)}")

            # Show first few signals
            print("   Recent signals:")
            for signal in signals[:5]:
                print(f"     {signal['timestamp'].strftime('%H:%M')} - {signal['signal']} @ ₹{signal['price']:.2f}")

        return {
            'prices': close_prices,
            'timestamps': timestamps,
            'ema5': ema5_values,
            'ema10': ema10_values,
            'signals': signals
        }

    except Exception as e:
        print(f"❌ Error validating EMA strategy: {e}")
        return None

def calculate_strategy_performance(validation_results):
    """Calculate strategy performance metrics"""
    print("\n📈 Calculating Strategy Performance...")

    signals = validation_results['signals']
    if not signals:
        print("⚠️  No signals to analyze")
        return

    # Calculate P&L
    trades = []
    position = None
    entry_price = None

    for signal in signals:
        if signal['signal'] == 'BUY' and position != 'LONG':
            if position == 'SHORT' and entry_price:
                # Close short position
                pnl = entry_price - signal['price']
                trades.append({'type': 'SHORT', 'entry': entry_price, 'exit': signal['price'], 'pnl': pnl})

            # Open long position
            position = 'LONG'
            entry_price = signal['price']

        elif signal['signal'] == 'SELL' and position != 'SHORT':
            if position == 'LONG' and entry_price:
                # Close long position
                pnl = signal['price'] - entry_price
                trades.append({'type': 'LONG', 'entry': entry_price, 'exit': signal['price'], 'pnl': pnl})

            # Open short position
            position = 'SHORT'
            entry_price = signal['price']

    if trades:
        total_pnl = sum(trade['pnl'] for trade in trades)
        winning_trades = [t for t in trades if t['pnl'] > 0]
        losing_trades = [t for t in trades if t['pnl'] <= 0]

        print(f"✅ Strategy Performance Analysis:")
        print(f"   Total trades: {len(trades)}")
        print(f"   Winning trades: {len(winning_trades)}")
        print(f"   Losing trades: {len(losing_trades)}")
        print(f"   Win rate: {len(winning_trades)/len(trades)*100:.1f}%")
        print(f"   Total P&L: ₹{total_pnl:.2f}")
        print(f"   Average P&L per trade: ₹{total_pnl/len(trades):.2f}")

        if winning_trades:
            avg_win = sum(t['pnl'] for t in winning_trades) / len(winning_trades)
            print(f"   Average winning trade: ₹{avg_win:.2f}")

        if losing_trades:
            avg_loss = sum(t['pnl'] for t in losing_trades) / len(losing_trades)
            print(f"   Average losing trade: ₹{avg_loss:.2f}")

    return trades

def create_visualization(validation_results):
    """Create visualization of the strategy"""
    print("\n📊 Creating Strategy Visualization...")

    try:
        prices = validation_results['prices']
        timestamps = validation_results['timestamps']
        ema5 = validation_results['ema5']
        ema10 = validation_results['ema10']
        signals = validation_results['signals']

        # Create the plot
        plt.style.use('dark_background')
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

        # Main price chart
        ax1.plot(timestamps, prices, label='NIFTY 50', color='white', linewidth=1)

        # Plot EMAs
        valid_ema5 = [(t, e) for t, e in zip(timestamps, ema5) if e is not None]
        valid_ema10 = [(t, e) for t, e in zip(timestamps, ema10) if e is not None]

        if valid_ema5:
            ema5_times, ema5_vals = zip(*valid_ema5)
            ax1.plot(ema5_times, ema5_vals, label='EMA 5', color='#00BFFF', linewidth=2)

        if valid_ema10:
            ema10_times, ema10_vals = zip(*valid_ema10)
            ax1.plot(ema10_times, ema10_vals, label='EMA 10', color='#FFA500', linewidth=2)

        # Plot signals
        for signal in signals:
            color = 'green' if signal['signal'] == 'BUY' else 'red'
            marker = '^' if signal['signal'] == 'BUY' else 'v'
            ax1.scatter(signal['timestamp'], signal['price'], color=color, marker=marker, s=100, zorder=5)

        ax1.set_title('NIFTY 50 EMA Strategy - Today\'s Validation', fontsize=16, fontweight='bold')
        ax1.set_ylabel('Price (₹)', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # EMA spread chart
        if valid_ema5 and valid_ema10 and len(valid_ema5) == len(valid_ema10):
            spread = [e5 - e10 for (_, e5), (_, e10) in zip(valid_ema5, valid_ema10)]
            spread_times = [t for t, _ in valid_ema5]

            ax2.plot(spread_times, spread, label='EMA5 - EMA10', color='yellow', linewidth=2)
            ax2.axhline(y=0, color='white', linestyle='--', alpha=0.5)
            ax2.fill_between(spread_times, spread, 0, alpha=0.3,
                           color='green' if spread[-1] > 0 else 'red')

        ax2.set_title('EMA Spread (EMA5 - EMA10)', fontsize=14)
        ax2.set_xlabel('Time', fontsize=12)
        ax2.set_ylabel('Spread (₹)', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()

        # Save the plot
        plot_file = os.path.join(os.path.dirname(__file__), '..', 'data', 'today_ema_validation.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"✅ Visualization saved: {plot_file}")

        plt.show()

    except Exception as e:
        print(f"❌ Error creating visualization: {e}")

def save_generated_data(price_data):
    """Save generated data to CSV file"""
    print("\n💾 Saving Generated Data...")

    try:
        # Convert to DataFrame
        df = pd.DataFrame(price_data)

        # Save to CSV
        today = datetime.now().strftime("%Y%m%d")
        csv_file = os.path.join(os.path.dirname(__file__), '..', 'data', f'generated_nifty_data_{today}.csv')

        df.to_csv(csv_file, index=False)
        print(f"✅ Data saved to: {csv_file}")

        return csv_file

    except Exception as e:
        print(f"❌ Error saving data: {e}")
        return None

def main():
    """Main function"""
    print("🚀 Generate and Validate Today's NIFTY 50 Data")
    print("=" * 60)

    try:
        # Step 1: Generate realistic data
        price_data = generate_realistic_nifty_data()

        # Step 2: Save generated data
        save_generated_data(price_data)

        # Step 3: Validate EMA strategy
        validation_results = validate_ema_strategy(price_data)

        if validation_results:
            # Step 4: Calculate performance
            calculate_strategy_performance(validation_results)

            # Step 5: Create visualization
            create_visualization(validation_results)

            print("\n" + "=" * 60)
            print("✅ VALIDATION COMPLETE")
            print("=" * 60)
            print("✅ Generated realistic NIFTY 50 data for today")
            print("✅ Validated EMA strategy with TradingView-compatible calculations")
            print("✅ Calculated strategy performance metrics")
            print("✅ Created visualization of results")

        else:
            print("\n❌ Validation failed")

    except Exception as e:
        print(f"\n❌ Error in main execution: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
