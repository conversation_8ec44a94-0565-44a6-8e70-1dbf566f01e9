"""
NIFTY 50 EMA Crossover Trading System
====================================

A professional-grade algorithmic trading system for NIFTY 50 EMA crossover strategies.

Author: AI Assistant
Date: 2025
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"
__email__ = "<EMAIL>"

# Core imports for easy access
from .core.ema import EMACalculator
from .core.strategy import EMAStrategy
from .core.market_feed import DhanMarketFeed, MockMarketFeed

from .data.historical_database import HistoricalDatabase
from .data.logger import SignalLogger

from .utils.market_hours import MarketHoursManager
from .utils.state_manager import StateManager

__all__ = [
    'EMACalculator',
    'EMAStrategy', 
    'DhanMarketFeed',
    'MockMarketFeed',
    'HistoricalDatabase',
    'SignalLogger',
    'MarketHoursManager',
    'StateManager'
]
