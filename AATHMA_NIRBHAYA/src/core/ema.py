#!/usr/bin/env python3
"""
EMA Calculator Module
====================

This module provides Exponential Moving Average (EMA) calculation functionality
for multiple timeframes and EMA combinations. It maintains historical data
and calculates EMAs efficiently for real-time trading signals.

Key Features:
- Multiple EMA period support (5, 8, 10, 12, 21, 26, etc.)
- Multiple timeframe support (1min, 5min, 10min, etc.)
- TradingView-compatible EMA calculation using pandas-ta
- Historical data management with pre-market data support
- Real-time updates from tick data
- Accurate crossover detection

Author: AI Assistant
Date: 2025
"""

import logging
from collections import defaultdict, deque
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np


class EMACalculator:
    """
    TradingView-Compatible Exponential Moving Average Calculator

    Calculates and maintains EMAs for multiple periods and timeframes using
    pandas-ta library for TradingView-compatible results. Supports pre-market
    data initialization and real-time updates.
    """

    def __init__(self, ema_combinations: List[Dict[str, int]], max_history: int = 1000):
        """
        Initialize EMA calculator

        Args:
            ema_combinations: List of EMA combinations like [{"short_ema": 5, "long_ema": 10}]
            max_history: Maximum number of historical values to keep
        """
        self.ema_combinations = ema_combinations
        self.max_history = max_history

        # Extract all unique EMA periods
        self.ema_periods = set()
        for combo in ema_combinations:
            self.ema_periods.add(combo['short_ema'])
            self.ema_periods.add(combo['long_ema'])

        # Storage for price data as pandas DataFrames by timeframe
        # Structure: {timeframe: DataFrame with columns ['price', 'ema5', 'ema10', ...]}
        self.price_data = defaultdict(lambda: pd.DataFrame(columns=['price']))

        # Storage for EMA values by timeframe and period (for backward compatibility)
        # Structure: {timeframe: {period: deque([ema_values])}}
        self.ema_values = defaultdict(lambda: defaultdict(lambda: deque(maxlen=max_history)))

        # Storage for price history by timeframe (for backward compatibility)
        # Structure: {timeframe: deque([prices])}
        self.price_history = defaultdict(lambda: deque(maxlen=max_history))

        # Track initialization status
        self.initialized = defaultdict(lambda: defaultdict(bool))

        # Minimum data points needed for reliable EMA calculation
        self.min_data_points = max(self.ema_periods) * 2 if self.ema_periods else 20

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"TradingView-Compatible EMA Calculator initialized with periods: {sorted(self.ema_periods)}")
        self.logger.info(f"Minimum data points required: {self.min_data_points}")

    def add_price(self, timeframe: str, price: float) -> Dict[int, float]:
        """
        Add a new price and calculate EMAs for all periods using TradingView-compatible method

        Args:
            timeframe: Timeframe identifier (e.g., "1min", "5min")
            price: New price value

        Returns:
            Dictionary of {period: ema_value} for all periods
        """
        if price <= 0:
            self.logger.warning(f"Invalid price {price} for timeframe {timeframe}")
            return {}

        # Add price to DataFrame
        df = self.price_data[timeframe]
        new_row = pd.DataFrame({'price': [price]}, index=[len(df)])
        self.price_data[timeframe] = pd.concat([df, new_row], ignore_index=True)

        # Keep only max_history rows
        if len(self.price_data[timeframe]) > self.max_history:
            self.price_data[timeframe] = self.price_data[timeframe].tail(self.max_history).reset_index(drop=True)

        # Add price to history (for backward compatibility)
        self.price_history[timeframe].append(price)

        # Calculate EMAs using pandas-ta for TradingView compatibility
        emas = self._calculate_tradingview_emas(timeframe)

        return emas

    def _calculate_tradingview_emas(self, timeframe: str) -> Dict[int, float]:
        """
        Calculate EMAs using TradingView-compatible method with pandas

        TradingView uses the standard EMA formula:
        EMA = (Close * Multiplier) + (Previous EMA * (1 - Multiplier))
        where Multiplier = 2 / (Period + 1)

        Args:
            timeframe: Timeframe identifier

        Returns:
            Dictionary of {period: ema_value} for all periods
        """
        df = self.price_data[timeframe]
        emas = {}

        if len(df) < self.min_data_points:
            # Not enough data for reliable EMA calculation
            return emas

        try:
            # Calculate EMAs for all periods using TradingView-compatible method
            for period in self.ema_periods:
                # TradingView-compatible EMA calculation
                # Initialize with first price, then use standard EMA formula
                ema_values = []
                multiplier = 2.0 / (period + 1)

                for i, price in enumerate(df['price']):
                    if i == 0:
                        # TradingView initializes EMA with the first price
                        ema_values.append(price)
                    else:
                        # Standard EMA formula: EMA = (Price * Multiplier) + (Previous EMA * (1 - Multiplier))
                        ema = (price * multiplier) + (ema_values[i-1] * (1 - multiplier))
                        ema_values.append(ema)

                # Convert to pandas Series
                ema_series = pd.Series(ema_values, index=df.index)

                if not ema_series.empty and not pd.isna(ema_series.iloc[-1]):
                    ema_value = ema_series.iloc[-1]
                    emas[period] = ema_value

                    # Update backward compatibility storage
                    self.ema_values[timeframe][period].append(ema_value)
                    self.initialized[timeframe][period] = True

                    # Store EMA in DataFrame for future calculations
                    df[f'ema{period}'] = ema_series

            # Update the DataFrame
            self.price_data[timeframe] = df

        except Exception as e:
            self.logger.error(f"Error calculating TradingView EMAs for {timeframe}: {e}")

        return emas

    def _calculate_ema(self, timeframe: str, period: int, price: float) -> Optional[float]:
        """
        Legacy EMA calculation method (kept for backward compatibility)

        Args:
            timeframe: Timeframe identifier
            period: EMA period
            price: Current price

        Returns:
            EMA value or None if not enough data
        """
        # Use the new TradingView-compatible method
        emas = self._calculate_tradingview_emas(timeframe)
        return emas.get(period)

    def get_latest_emas(self, timeframe: str) -> Dict[int, float]:
        """
        Get the latest EMA values for all periods in a timeframe

        Args:
            timeframe: Timeframe identifier

        Returns:
            Dictionary of {period: latest_ema_value}
        """
        emas = {}
        for period in self.ema_periods:
            if self.ema_values[timeframe][period]:
                emas[period] = self.ema_values[timeframe][period][-1]
        return emas

    def get_ema_history(self, timeframe: str, period: int, count: int = 10) -> List[float]:
        """
        Get historical EMA values

        Args:
            timeframe: Timeframe identifier
            period: EMA period
            count: Number of historical values to return

        Returns:
            List of historical EMA values (most recent first)
        """
        history = self.ema_values[timeframe][period]
        return list(history)[-count:] if history else []

    def is_ready(self, timeframe: str, period: int) -> bool:
        """
        Check if EMA is ready for a specific period and timeframe

        Args:
            timeframe: Timeframe identifier
            period: EMA period

        Returns:
            True if EMA is initialized and ready
        """
        return self.initialized[timeframe][period] and len(self.ema_values[timeframe][period]) > 0

    def get_crossover_signals(self, timeframe: str) -> List[Dict]:
        """
        Detect EMA crossover signals for all combinations

        Args:
            timeframe: Timeframe identifier

        Returns:
            List of crossover signals with details
        """
        signals = []
        MIN_CROSSOVER_DIFF = 0.5  # Minimum difference between EMAs to generate signal

        for combo in self.ema_combinations:
            short_period = combo['short_ema']
            long_period = combo['long_ema']

            # Check if both EMAs are ready
            if not (self.is_ready(timeframe, short_period) and self.is_ready(timeframe, long_period)):
                continue

            # Get current and previous EMA values
            short_history = self.ema_values[timeframe][short_period]
            long_history = self.ema_values[timeframe][long_period]

            if len(short_history) >= 2 and len(long_history) >= 2:
                # Current values
                short_current = short_history[-1]
                long_current = long_history[-1]

                # Previous values
                short_previous = short_history[-2]
                long_previous = long_history[-2]

                # Calculate difference between EMAs
                current_diff = abs(short_current - long_current)

                # Detect crossover with minimum threshold
                signal_type = None
                if (short_previous <= long_previous and short_current > long_current and
                    current_diff >= MIN_CROSSOVER_DIFF):
                    signal_type = "BUY"  # Golden Cross
                elif (short_previous >= long_previous and short_current < long_current and
                      current_diff >= MIN_CROSSOVER_DIFF):
                    signal_type = "SELL"  # Death Cross

                if signal_type:
                    signals.append({
                        'timeframe': timeframe,
                        'signal': signal_type,
                        'short_ema': short_period,
                        'long_ema': long_period,
                        'short_value': short_current,
                        'long_value': long_current,
                        'diff': current_diff,
                        'price': self.price_history[timeframe][-1] if self.price_history[timeframe] else None
                    })

        return signals

    def get_statistics(self, timeframe: str) -> Dict:
        """
        Get statistics for a timeframe

        Args:
            timeframe: Timeframe identifier

        Returns:
            Dictionary with statistics
        """
        stats = {
            'timeframe': timeframe,
            'price_count': len(self.price_history[timeframe]),
            'latest_price': self.price_history[timeframe][-1] if self.price_history[timeframe] else None,
            'ema_status': {}
        }

        for period in sorted(self.ema_periods):
            stats['ema_status'][f'EMA{period}'] = {
                'ready': self.is_ready(timeframe, period),
                'count': len(self.ema_values[timeframe][period]),
                'latest': self.ema_values[timeframe][period][-1] if self.ema_values[timeframe][period] else None
            }

        return stats

    def reset_timeframe(self, timeframe: str):
        """
        Reset all data for a specific timeframe

        Args:
            timeframe: Timeframe identifier to reset
        """
        # Reset DataFrame
        self.price_data[timeframe] = pd.DataFrame(columns=['price'])

        # Reset backward compatibility storage
        self.price_history[timeframe].clear()
        for period in self.ema_periods:
            self.ema_values[timeframe][period].clear()
            self.initialized[timeframe][period] = False

        self.logger.info(f"Reset data for timeframe: {timeframe}")

    def reset_all(self):
        """Reset all data for all timeframes"""
        self.price_data.clear()
        self.price_history.clear()
        self.ema_values.clear()
        self.initialized.clear()
        self.logger.info("Reset all EMA calculator data")

    def load_state_from_prices(self, timeframe: str, historical_prices: List[float]):
        """
        Reconstruct EMA state from historical prices using TradingView-compatible method

        Args:
            timeframe: Timeframe to load state for
            historical_prices: List of historical prices in chronological order
        """
        try:
            if not historical_prices:
                self.logger.warning(f"No historical prices provided for {timeframe}")
                return

            self.logger.info(f"Loading EMA state from {len(historical_prices)} historical prices for {timeframe}")

            # Reset current state for this timeframe
            self.reset_timeframe(timeframe)

            # Load all historical prices into DataFrame at once for better performance
            df = pd.DataFrame({'price': historical_prices})
            self.price_data[timeframe] = df

            # Calculate EMAs for the entire dataset using TradingView-compatible method
            try:
                for period in self.ema_periods:
                    # TradingView-compatible EMA calculation
                    ema_values = []
                    multiplier = 2.0 / (period + 1)

                    for i, price in enumerate(df['price']):
                        if i == 0:
                            # TradingView initializes EMA with the first price
                            ema_values.append(price)
                        else:
                            # Standard EMA formula
                            ema = (price * multiplier) + (ema_values[i-1] * (1 - multiplier))
                            ema_values.append(ema)

                    ema_series = pd.Series(ema_values, index=df.index)
                    df[f'ema{period}'] = ema_series

                    # Update backward compatibility storage with all values
                    for ema_val in ema_values:
                        self.ema_values[timeframe][period].append(ema_val)

                    if ema_values:
                        self.initialized[timeframe][period] = True

                # Update the DataFrame
                self.price_data[timeframe] = df

                # Update price history for backward compatibility
                self.price_history[timeframe].extend(historical_prices)

                # Log final state
                current_emas = self.get_current_ema_values(timeframe)
                if current_emas:
                    ema_info = [f"{k}={v:.2f}" for k, v in current_emas.items()]
                    self.logger.info(f"EMA state loaded for {timeframe}: {', '.join(ema_info)}")

            except Exception as e:
                self.logger.error(f"Error calculating EMAs during state loading: {e}")

        except Exception as e:
            self.logger.error(f"Error loading EMA state from historical prices: {e}")

    def load_premarket_data(self, timeframe: str, premarket_prices: List[float]):
        """
        Load pre-market data to properly initialize EMAs before market open

        Args:
            timeframe: Timeframe to load data for
            premarket_prices: List of pre-market prices in chronological order
        """
        if not premarket_prices:
            self.logger.warning(f"No pre-market prices provided for {timeframe}")
            return

        self.logger.info(f"Loading {len(premarket_prices)} pre-market prices for {timeframe}")
        self.load_state_from_prices(timeframe, premarket_prices)

    def get_current_ema_values(self, timeframe: str) -> Dict[str, float]:
        """
        Get current EMA values for a timeframe

        Returns:
            Dictionary with current EMA values
        """
        result = {}

        if timeframe not in self.ema_values:
            return result

        for period in self.ema_periods:
            if (period in self.ema_values[timeframe] and
                self.ema_values[timeframe][period] and
                self.initialized[timeframe][period]):
                result[f"EMA{period}"] = self.ema_values[timeframe][period][-1]

        return result