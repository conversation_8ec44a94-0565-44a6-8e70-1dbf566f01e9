#!/usr/bin/env python3
"""
DhanHQ Market Feed Module
========================

This module handles real-time market data streaming from DhanHQ using WebSocket.
It connects to DhanHQ's live market feed, subscribes to NIFTY 50 data,
and forwards tick data to the trading strategy for processing.

Key Features:
- WebSocket connection to DhanHQ live market feed
- Automatic reconnection on disconnects
- NIFTY 50 instrument subscription
- Real-time tick data processing
- Error handling and logging
- Connection status monitoring

Author: AI Assistant
Date: 2025
"""

import json
import logging
import time
import threading
from datetime import datetime
from typing import Dict, Optional, Callable
# Optional imports for DhanHQ integration
try:
    import websocket
    from dhanhq import dhanhq
    DHANHQ_AVAILABLE = True
except ImportError:
    websocket = None
    dhanhq = None
    DHANHQ_AVAILABLE = False


class DhanMarketFeed:
    """
    DhanHQ WebSocket Market Feed Client

    Connects to DhanHQ's live market feed and streams real-time tick data
    for NIFTY 50 to the trading strategy.
    """

    def __init__(self, client_id: str, access_token: str, instrument: Dict, strategy):
        """
        Initialize DhanHQ market feed

        Args:
            client_id: DhanHQ client ID
            access_token: DhanHQ access token
            instrument: Instrument configuration
            strategy: Strategy instance to receive tick data
        """
        if not DHANHQ_AVAILABLE:
            raise ImportError("DhanHQ and websocket libraries are required for live market feed. "
                            "Install with: pip install dhanhq websocket-client")

        self.client_id = client_id
        self.access_token = access_token
        self.instrument = instrument
        self.strategy = strategy

        # DhanHQ client
        self.dhan = dhanhq(client_id, access_token)

        # WebSocket connection
        self.ws = None
        self.ws_url = f"wss://api-feed.dhan.co?version=2&token={access_token}&clientId={client_id}&authType=2"

        # Connection state
        self.connected = False
        self.running = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        self.reconnect_delay = 5  # seconds

        # Statistics
        self.stats = {
            'ticks_received': 0,
            'connection_time': None,
            'last_tick_time': None,
            'reconnections': 0
        }

        # Threading
        self.ws_thread = None
        self.heartbeat_thread = None

        self.logger = logging.getLogger(__name__)
        self.logger.info(f"DhanHQ Market Feed initialized for {instrument['name']}")

    def start(self):
        """Start the market feed"""
        self.logger.info("Starting DhanHQ market feed...")
        self.running = True

        # Validate instrument
        if not self._validate_instrument():
            return False

        # Start WebSocket connection
        self._connect_websocket()

        # Start connection monitor thread
        self.monitor_thread = threading.Thread(target=self._connection_monitor, daemon=True)
        self.monitor_thread.start()

        return True

    def stop(self):
        """Stop the market feed"""
        self.logger.info("Stopping DhanHQ market feed...")
        self.running = False

        if self.ws:
            self.ws.close()

        self.connected = False
        self.logger.info("Market feed stopped")

    def _validate_instrument(self) -> bool:
        """Validate instrument configuration"""
        try:
            # For now, we'll use the configured security ID
            # In a real implementation, you might want to fetch and validate from DhanHQ
            security_id = self.instrument.get('security_id')
            exchange_segment = self.instrument.get('exchange_segment')

            if not security_id or not exchange_segment:
                self.logger.error("Invalid instrument configuration")
                return False

            self.logger.info(f"Validated instrument: {self.instrument['name']} "
                           f"(ID: {security_id}, Exchange: {exchange_segment})")
            return True

        except Exception as e:
            self.logger.error(f"Error validating instrument: {e}")
            return False

    def _connect_websocket(self):
        """Connect to DhanHQ WebSocket"""
        try:
            self.logger.info("Connecting to DhanHQ WebSocket...")

            # Create WebSocket connection
            websocket.enableTrace(False)  # Set to True for debugging

            self.ws = websocket.WebSocketApp(
                self.ws_url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )

            # Start WebSocket in a separate thread
            # DhanHQ handles ping/pong automatically, so we don't need manual ping
            self.ws_thread = threading.Thread(
                target=self.ws.run_forever,
                kwargs={
                    'ping_interval': 30,  # Send ping every 30 seconds
                    'ping_timeout': 10,   # Wait 10 seconds for pong
                    'ping_payload': b'ping'  # Custom ping payload
                },
                daemon=True
            )
            self.ws_thread.start()

        except Exception as e:
            self.logger.error(f"Error connecting to WebSocket: {e}")
            self._schedule_reconnect()

    def _on_open(self, ws):
        """WebSocket connection opened"""
        self.logger.info("✅ WebSocket connected to DhanHQ")
        self.connected = True
        self.reconnect_attempts = 0
        self.stats['connection_time'] = datetime.now()

        # Subscribe to instrument
        self._subscribe_to_instrument()

    def _on_message(self, ws, message):
        """Handle incoming WebSocket message"""
        try:
            # DhanHQ sends binary data, not JSON
            if isinstance(message, bytes):
                self._process_binary_message(message)
            else:
                # Handle any text messages (like connection confirmations)
                self.logger.debug(f"Received text message: {message}")

        except Exception as e:
            self.logger.error(f"Error processing message: {e}")

    def _process_binary_message(self, message: bytes):
        """Process binary message from DhanHQ"""
        try:
            import struct

            if len(message) < 8:
                self.logger.warning(f"Message too short: {len(message)} bytes")
                return

            # Parse response header (8 bytes)
            feed_response_code = message[0]
            message_length = struct.unpack('<H', message[1:3])[0]  # Little-endian 16-bit
            exchange_segment = message[3]
            security_id = struct.unpack('<I', message[4:8])[0]  # Little-endian 32-bit

            self.logger.debug(f"Binary message - Code: {feed_response_code}, Length: {message_length}, "
                            f"Exchange: {exchange_segment}, Security: {security_id}")

            # Process different message types
            if feed_response_code == 2:  # Ticker packet
                self._process_ticker_packet(message[8:])
            elif feed_response_code == 4:  # Quote packet
                self._process_quote_packet(message[8:])
            elif feed_response_code == 6:  # Previous close packet
                self._process_prev_close_packet(message[8:])
            elif feed_response_code == 50:  # Disconnection packet
                self._process_disconnection_packet(message[8:])
            else:
                self.logger.debug(f"Unknown feed response code: {feed_response_code}")

        except Exception as e:
            self.logger.error(f"Error processing binary message: {e}")

    def _process_ticker_packet(self, payload: bytes):
        """Process ticker packet (LTP data)"""
        try:
            import struct

            if len(payload) < 8:
                return

            # Parse ticker data
            ltp = struct.unpack('<f', payload[0:4])[0]  # Last Traded Price
            ltt = struct.unpack('<I', payload[4:8])[0]  # Last Trade Time

            # Create tick data
            tick_data = {
                'timestamp': ltt if ltt > 0 else time.time(),
                'price': ltp,
                'volume': 1,  # Default volume for ticker
                'security_id': self.instrument['security_id'],
                'exchange_segment': self.instrument['exchange_segment']
            }

            # Forward to strategy
            if ltp > 0:
                self.strategy.process_tick(tick_data)
                self.stats['ticks_received'] += 1
                self.stats['last_tick_time'] = datetime.now()
                self.logger.debug(f"Ticker: LTP={ltp:.2f}, LTT={ltt}")

        except Exception as e:
            self.logger.error(f"Error processing ticker packet: {e}")

    def _process_quote_packet(self, payload: bytes):
        """Process quote packet (detailed trade data)"""
        try:
            import struct

            if len(payload) < 42:  # Minimum quote packet size
                return

            # Parse quote data
            ltp = struct.unpack('<f', payload[0:4])[0]  # Last Traded Price
            ltq = struct.unpack('<H', payload[4:6])[0]  # Last Traded Quantity
            ltt = struct.unpack('<I', payload[6:10])[0]  # Last Trade Time
            atp = struct.unpack('<f', payload[10:14])[0]  # Average Trade Price
            volume = struct.unpack('<I', payload[14:18])[0]  # Volume

            # Create detailed tick data
            tick_data = {
                'timestamp': ltt if ltt > 0 else time.time(),
                'price': ltp,
                'volume': ltq if ltq > 0 else 1,
                'total_volume': volume,
                'atp': atp,
                'security_id': self.instrument['security_id'],
                'exchange_segment': self.instrument['exchange_segment']
            }

            # Forward to strategy
            if ltp > 0:
                self.strategy.process_tick(tick_data)
                self.stats['ticks_received'] += 1
                self.stats['last_tick_time'] = datetime.now()
                self.logger.debug(f"Quote: LTP={ltp:.2f}, Volume={volume}, ATP={atp:.2f}")

        except Exception as e:
            self.logger.error(f"Error processing quote packet: {e}")

    def _process_prev_close_packet(self, payload: bytes):
        """Process previous close packet"""
        try:
            import struct

            if len(payload) < 8:
                return

            prev_close = struct.unpack('<f', payload[0:4])[0]
            prev_oi = struct.unpack('<I', payload[4:8])[0]

            self.logger.info(f"Previous close: {prev_close:.2f}, Previous OI: {prev_oi}")

        except Exception as e:
            self.logger.error(f"Error processing prev close packet: {e}")

    def _process_disconnection_packet(self, payload: bytes):
        """Process disconnection packet"""
        try:
            import struct

            if len(payload) < 2:
                return

            disconnect_code = struct.unpack('<H', payload[0:2])[0]
            self.logger.warning(f"Disconnection code: {disconnect_code}")

        except Exception as e:
            self.logger.error(f"Error processing disconnection packet: {e}")

    def _on_error(self, ws, error):
        """Handle WebSocket error"""
        self.logger.error(f"WebSocket error: {error}")
        self.connected = False

    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        self.logger.warning(f"WebSocket closed: {close_status_code} - {close_msg}")
        self.connected = False

        if self.running:
            self._schedule_reconnect()

    def _subscribe_to_instrument(self):
        """Subscribe to instrument data"""
        try:
            # Subscribe to Ticker data (RequestCode 15) for real-time LTP
            ticker_subscription = {
                "RequestCode": 15,  # Ticker data
                "InstrumentCount": 1,
                "InstrumentList": [
                    {
                        "ExchangeSegment": self.instrument['exchange_segment'],
                        "SecurityId": self.instrument['security_id']
                    }
                ]
            }

            # Subscribe to Quote data (RequestCode 17) for more detailed data
            quote_subscription = {
                "RequestCode": 17,  # Quote data
                "InstrumentCount": 1,
                "InstrumentList": [
                    {
                        "ExchangeSegment": self.instrument['exchange_segment'],
                        "SecurityId": self.instrument['security_id']
                    }
                ]
            }

            # Send both subscriptions
            self.ws.send(json.dumps(ticker_subscription))
            self.ws.send(json.dumps(quote_subscription))

            self.logger.info(f"Subscribed to {self.instrument['name']} "
                           f"(ID: {self.instrument['security_id']}, Exchange: {self.instrument['exchange_segment']})")

        except Exception as e:
            self.logger.error(f"Error subscribing to instrument: {e}")



    def _schedule_reconnect(self):
        """Schedule WebSocket reconnection"""
        if not self.running or self.reconnect_attempts >= self.max_reconnect_attempts:
            return

        self.reconnect_attempts += 1
        self.stats['reconnections'] += 1

        self.logger.info(f"Scheduling reconnection attempt {self.reconnect_attempts} "
                        f"in {self.reconnect_delay} seconds...")

        # Reconnect after delay
        threading.Timer(self.reconnect_delay, self._connect_websocket).start()

    def _connection_monitor(self):
        """Monitor connection status and statistics"""
        while self.running:
            try:
                # Log connection statistics periodically
                if self.stats['ticks_received'] > 0:
                    self.logger.debug(f"Connection status: Connected={self.connected}, "
                                    f"Ticks received={self.stats['ticks_received']}, "
                                    f"Last tick: {self.stats['last_tick_time']}")

                # Check if we haven't received ticks for a while (during market hours)
                if (self.connected and self.stats['last_tick_time'] and
                    (datetime.now() - self.stats['last_tick_time']).seconds > 300):  # 5 minutes
                    self.logger.warning("No ticks received for 5 minutes, connection might be stale")

                time.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"Connection monitor error: {e}")
                time.sleep(30)

    def get_connection_status(self) -> Dict:
        """Get connection status and statistics"""
        return {
            'connected': self.connected,
            'running': self.running,
            'reconnect_attempts': self.reconnect_attempts,
            'stats': self.stats.copy()
        }

    def is_connected(self) -> bool:
        """Check if WebSocket is connected"""
        return self.connected and self.ws is not None

    def get_statistics(self) -> Dict:
        """Get market feed statistics"""
        return self.stats.copy()


# Mock market feed for testing without DhanHQ connection
class MockMarketFeed:
    """
    Mock market feed for testing and development

    Simulates real-time NIFTY 50 tick data for testing the EMA strategy
    without requiring actual DhanHQ connection.
    """

    def __init__(self, instrument: Dict, strategy):
        """Initialize mock market feed"""
        self.instrument = instrument
        self.strategy = strategy
        self.running = False

        # Simulation parameters
        self.base_price = 19500.0  # Starting NIFTY 50 price
        self.current_price = self.base_price
        self.tick_interval = 1.0  # seconds between ticks

        # Statistics
        self.stats = {
            'ticks_sent': 0,
            'start_time': None,
            'last_tick_time': None
        }

        self.logger = logging.getLogger(__name__)
        self.logger.info("Mock Market Feed initialized for testing")

    def start(self):
        """Start mock market feed"""
        self.logger.info("Starting mock market feed...")
        self.running = True
        self.stats['start_time'] = datetime.now()

        # Start simulation thread
        simulation_thread = threading.Thread(target=self._simulate_ticks, daemon=True)
        simulation_thread.start()

        return True

    def stop(self):
        """Stop mock market feed"""
        self.logger.info("Stopping mock market feed...")
        self.running = False

    def _simulate_ticks(self):
        """Simulate real-time tick data with trending movements to generate crossovers"""
        import random

        # Create a trending pattern to generate EMA crossovers
        trend_direction = 1  # 1 for up, -1 for down
        trend_duration = 0
        max_trend_duration = 50  # Change trend every 50 ticks

        while self.running:
            try:
                # Change trend direction periodically to create crossovers
                trend_duration += 1
                if trend_duration >= max_trend_duration:
                    trend_direction *= -1  # Reverse trend
                    trend_duration = 0
                    max_trend_duration = random.randint(30, 70)  # Vary trend duration
                    self.logger.info(f"Mock feed: Trend changed to {'UP' if trend_direction > 0 else 'DOWN'}")

                # Generate price movement with trend bias
                if trend_direction > 0:
                    # Upward trend with some noise
                    change_percent = random.uniform(0.0005, 0.003)  # 0.05% to 0.3% up
                else:
                    # Downward trend with some noise
                    change_percent = random.uniform(-0.003, -0.0005)  # 0.05% to 0.3% down

                # Add some random noise
                noise = random.uniform(-0.001, 0.001)  # ±0.1% noise
                total_change = change_percent + noise

                self.current_price *= (1 + total_change)

                # Keep price in reasonable range
                if self.current_price < self.base_price * 0.95:
                    self.current_price = self.base_price * 0.95
                    trend_direction = 1  # Force upward
                elif self.current_price > self.base_price * 1.05:
                    self.current_price = self.base_price * 1.05
                    trend_direction = -1  # Force downward

                # Create tick data
                tick_data = {
                    'timestamp': time.time(),
                    'price': round(self.current_price, 2),
                    'volume': random.randint(50, 200),
                    'security_id': self.instrument['security_id'],
                    'exchange_segment': self.instrument['exchange_segment']
                }

                # Send to strategy
                self.strategy.process_tick(tick_data)

                # Update statistics
                self.stats['ticks_sent'] += 1
                self.stats['last_tick_time'] = datetime.now()

                # Log every 10th tick to show progress
                if self.stats['ticks_sent'] % 10 == 0:
                    self.logger.info(f"Mock tick #{self.stats['ticks_sent']}: Price={self.current_price:.2f}, "
                                   f"Trend={'UP' if trend_direction > 0 else 'DOWN'}")

                # Wait for next tick
                time.sleep(self.tick_interval)

            except Exception as e:
                self.logger.error(f"Error in mock simulation: {e}")
                time.sleep(1)

    def get_connection_status(self) -> Dict:
        """Get mock connection status"""
        return {
            'connected': self.running,
            'running': self.running,
            'reconnect_attempts': 0,
            'stats': self.stats.copy()
        }

    def is_connected(self) -> bool:
        """Check if mock feed is running"""
        return self.running

    def get_statistics(self) -> Dict:
        """Get mock feed statistics"""
        return self.stats.copy()