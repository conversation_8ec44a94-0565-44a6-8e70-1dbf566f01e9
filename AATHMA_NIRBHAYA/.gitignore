# NIFTY 50 EMA Crossover Trading System
# .gitignore file

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Trading System Specific
# ======================

# Configuration files with sensitive data
config/config.json
config/credentials.json
*.key
*.secret

# Data files
data/
logs/
*.csv
*.pkl
*.db
*.sqlite

# Trading logs and state files
*.pid
*.lock
session_state.pkl
ema_state_*.pkl

# Backup files
*.bak
*.backup
*~

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Performance profiling
*.prof
*.profile

# Security scanning
.bandit
.safety

# Local development
local/
sandbox/
playground/

# Documentation build
docs/build/
docs/_build/

# Test artifacts
test_data/
demo_data/
*.test
test_*.csv
test_*.pkl

# Historical data (keep structure, ignore content)
data/historical/*.pkl
data/historical/*.csv
!data/historical/.gitkeep

# Signal files (keep structure, ignore content)  
data/signals/*.csv
!data/signals/.gitkeep

# Log files (keep structure, ignore content)
logs/*.log
!logs/.gitkeep

# State files (keep structure, ignore content)
data/state/*.pkl
!data/state/.gitkeep
